CREATE TABLE IF NOT EXISTS nmap_scans_json_result (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,  -- 支持IPv6的最大长度
    result JSON,
    analyse_status VARCHAR(45) NOT NULL DEFAULT 'todo',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_scan (ip_address)
);


---这个表干脆不要了，后面直接处理
CREATE TABLE IF NOT EXISTS nmap_scan_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    port INT ,
    protocol  VARCHAR(45),
    service_name VARCHAR(255),
    service_version VARCHAR(255),
    state ENUM('open', 'closed', 'filtered') NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);