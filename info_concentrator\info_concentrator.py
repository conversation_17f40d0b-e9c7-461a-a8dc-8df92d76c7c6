'''
信息集中器模块

功能：
1. 从数据库中获取要扫描的IP
2. 针对每个IP进行扫描，获取网络信息和Web信息
   - 网络信息：IP、MAC、厂商、端口、服务、操作系统
   - Web信息：IP、域名、标题、关键字、描述、指纹、漏洞、网页内容
3. 连接大模型进行分析，识别可能存在的漏洞及验证路径
4. 生成攻击路径和实现代码
5. 执行攻击并验证效果
6. 保存成功的攻击路径和代码到数据库

数据流：
1. 获取目标IP -> 2. 收集信息 -> 3. 分析信息 -> 4. 生成攻击路径 -> 5. 执行攻击 -> 6. 验证结果
'''

import os
import sys
import json
import time
import pymysql
import configparser
from typing import Dict, List, Any, Tuple, Optional
from openai import OpenAI

# 添加项目根目录到系统路径，以便导入其他模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 导入信息收集模块
from info_collector.nmap.nmap_task import insert_scan_task
from info_collector.nmap.nmapscan import scan_ip_segment
from info_collector.web.get_domain_task import get_ip_segments
from info_collector.web.api_scan import scan_api
from info_collector.web.web_content_spider import crawl_website
from info_collector.web.awvs_scan import start_awvs_scan

# 导入信息分析模块
from info_concentrator.effective_info_extraction import get_db_connection, analyze_with_llm, save_analysis_result


def get_llm_client() -> OpenAI:
    """获取LLM客户端"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'config.config')
    config.read(config_path, encoding='utf-8')
    
    api_key = config['llm']['api_key']
    base_url = config['llm']['base_url']
    
    return OpenAI(
        api_key=api_key,
        base_url=base_url
    )


def get_target_ips(limit: int = 10) -> List[str]:
    """从数据库获取待扫描的目标IP列表"""
    try:
        db = get_db_connection()
        cursor = db.cursor()
        
        # 从ip_segment_task表中获取状态为'todo'的IP段
        query = "SELECT ip_segment FROM ip_segment_task WHERE status = 'todo' LIMIT %s"
        cursor.execute(query, (limit,))
        ip_segments = cursor.fetchall()
        
        cursor.close()
        db.close()
        
        # 返回IP段列表
        return [segment[0] for segment in ip_segments]
    except Exception as e:
        print(f"获取目标IP时出错: {e}")
        return []


def update_ip_segment_status(ip_segment: str, status: str) -> bool:
    """更新IP段的扫描状态"""
    try:
        db = get_db_connection()
        cursor = db.cursor()
        
        query = "UPDATE ip_segment_task SET status = %s WHERE ip_segment = %s"
        cursor.execute(query, (status, ip_segment))
        db.commit()
        
        cursor.close()
        db.close()
        return True
    except Exception as e:
        print(f"更新IP段状态时出错: {e}")
        return False


def collect_network_info(ip_segment: str) -> Dict[str, Any]:
    """收集网络信息"""
    print(f"开始收集网络信息: {ip_segment}")
    try:
        # 使用nmap扫描IP段
        scan_results = scan_ip_segment(ip_segment)
        
        # 整理扫描结果
        network_info = {
            'ip_segment': ip_segment,
            'scan_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'hosts': []
        }
        
        # 解析扫描结果，提取每个主机的信息
        if scan_results and 'scan' in scan_results:
            for ip, host_data in scan_results['scan'].items():
                host_info = {
                    'ip': ip,
                    'status': host_data.get('status', {}).get('state', 'unknown'),
                    'mac': host_data.get('addresses', {}).get('mac', ''),
                    'vendor': host_data.get('vendor', {}).get(host_data.get('addresses', {}).get('mac', ''), ''),
                    'os': [],
                    'ports': []
                }
                
                # 提取操作系统信息
                if 'osmatch' in host_data:
                    for os_match in host_data['osmatch']:
                        host_info['os'].append({
                            'name': os_match.get('name', ''),
                            'accuracy': os_match.get('accuracy', '')
                        })
                
                # 提取端口和服务信息
                if 'tcp' in host_data:
                    for port, port_data in host_data['tcp'].items():
                        host_info['ports'].append({
                            'port': port,
                            'protocol': 'tcp',
                            'state': port_data.get('state', ''),
                            'service': port_data.get('name', ''),
                            'product': port_data.get('product', ''),
                            'version': port_data.get('version', ''),
                            'cpe': port_data.get('cpe', [])
                        })
                
                if 'udp' in host_data:
                    for port, port_data in host_data['udp'].items():
                        host_info['ports'].append({
                            'port': port,
                            'protocol': 'udp',
                            'state': port_data.get('state', ''),
                            'service': port_data.get('name', ''),
                            'product': port_data.get('product', ''),
                            'version': port_data.get('version', ''),
                            'cpe': port_data.get('cpe', [])
                        })
                
                network_info['hosts'].append(host_info)
        
        return network_info
    except Exception as e:
        print(f"收集网络信息时出错: {e}")
        return {'ip_segment': ip_segment, 'error': str(e), 'hosts': []}


def collect_web_info(ip: str) -> Dict[str, Any]:
    """收集Web信息"""
    print(f"开始收集Web信息: {ip}")
    web_info = {
        'ip': ip,
        'scan_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'domains': [],
        'web_services': []
    }
    
    try:
        # 获取与IP关联的域名
        db = get_db_connection()
        cursor = db.cursor()
        
        query = "SELECT domain FROM domain_ip_mapping WHERE ip = %s"
        cursor.execute(query, (ip,))
        domains = cursor.fetchall()
        
        cursor.close()
        db.close()
        
        # 对每个域名进行Web信息收集
        for domain in domains:
            domain_name = domain[0]
            web_info['domains'].append(domain_name)
            
            # 使用API扫描获取Web服务信息
            api_results = scan_api(domain_name)
            
            # 使用爬虫获取网页内容
            spider_results = crawl_website(domain_name)
            
            # 启动AWVS扫描
            awvs_scan_id = start_awvs_scan(domain_name)
            
            # 整合Web服务信息
            web_service = {
                'domain': domain_name,
                'title': spider_results.get('title', ''),
                'keywords': spider_results.get('keywords', ''),
                'description': spider_results.get('description', ''),
                'server': api_results.get('server', ''),
                'technologies': api_results.get('technologies', []),
                'vulnerabilities': [],
                'content_summary': spider_results.get('content_summary', ''),
                'awvs_scan_id': awvs_scan_id
            }
            
            web_info['web_services'].append(web_service)
        
        return web_info
    except Exception as e:
        print(f"收集Web信息时出错: {e}")
        return {'ip': ip, 'error': str(e), 'domains': [], 'web_services': []}


def analyze_security_info(network_info: Dict[str, Any], web_info: Dict[str, Any]) -> Dict[str, Any]:
    """使用大模型分析安全信息"""
    print("开始分析安全信息...")
    try:
        # 整合网络和Web信息
        combined_info = {
            'network_info': network_info,
            'web_info': web_info,
            'analysis_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 使用大模型分析
        analysis_result = analyze_with_llm(combined_info)
        
        return analysis_result
    except Exception as e:
        print(f"分析安全信息时出错: {e}")
        return {'status': 'error', 'details': str(e)}


def generate_attack_path(analysis_result: Dict[str, Any]) -> Dict[str, Any]:
    """生成攻击路径和实现代码"""
    print("开始生成攻击路径...")
    try:
        client = get_llm_client()
        
        # 构建提示词
        prompt = f"""
你是一名专业的渗透测试专家，请基于以下安全分析结果，生成详细的攻击路径和实现代码：

### 安全分析结果
{analysis_result['analysis']}

请提供：
1. 攻击路径：详细描述如何利用发现的漏洞进行攻击，包括每个步骤的技术细节和预期结果
2. 实现代码：提供可执行的Python代码，用于验证和利用这些漏洞
3. 成功指标：说明如何判断攻击是否成功

输出格式：
```json
{{
  "attack_paths": [
    {{
      "name": "攻击路径名称",
      "description": "详细描述",
      "steps": [
        {{
          "step": 1,
          "description": "步骤描述",
          "technical_details": "技术细节"
        }},
        // 更多步骤...
      ]
    }},
    // 更多攻击路径...
  ],
  "exploit_code": {{
    "language": "python",
    "code": "完整的Python代码，包含必要的导入和注释"
  }},
  "success_criteria": "如何判断攻击是否成功的详细说明"
}}
```
"""
        
        # 调用大模型生成攻击路径
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are a cybersecurity expert specialized in penetration testing and exploit development."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2
        )
        
        # 解析响应
        attack_path_text = response.choices[0].message.content
        
        # 提取JSON部分
        try:
            # 尝试直接解析整个文本
            attack_path = json.loads(attack_path_text)
        except json.JSONDecodeError:
            # 如果失败，尝试提取```json和```之间的内容
            import re
            json_match = re.search(r'```json\n(.+?)\n```', attack_path_text, re.DOTALL)
            if json_match:
                attack_path = json.loads(json_match.group(1))
            else:
                # 如果仍然失败，返回原始文本
                attack_path = {"raw_response": attack_path_text}
        
        return {
            "status": "success",
            "attack_path": attack_path
        }
    except Exception as e:
        print(f"生成攻击路径时出错: {e}")
        return {"status": "error", "details": str(e)}


def execute_attack(attack_path: Dict[str, Any]) -> Dict[str, Any]:
    """执行攻击并验证效果"""
    print("开始执行攻击...")
    try:
        # 提取攻击代码
        if 'attack_path' in attack_path and 'exploit_code' in attack_path['attack_path']:
            exploit_code = attack_path['attack_path']['exploit_code']['code']
            
            # 保存攻击代码到临时文件
            temp_file = os.path.join(os.path.dirname(__file__), 'temp_exploit.py')
            with open(temp_file, 'w') as f:
                f.write(exploit_code)
            
            # 执行攻击代码
            print("执行攻击代码...")
            import subprocess
            result = subprocess.run([sys.executable, temp_file], capture_output=True, text=True)
            
            # 删除临时文件
            os.remove(temp_file)
            
            # 检查执行结果
            if result.returncode == 0:
                # 根据成功指标判断攻击是否成功
                success_criteria = attack_path['attack_path'].get('success_criteria', '')
                output = result.stdout
                
                # 简单判断：如果输出中包含'success'或'成功'，则认为攻击成功
                if 'success' in output.lower() or '成功' in output:
                    return {
                        "status": "success",
                        "details": "攻击成功执行",
                        "output": output
                    }
                else:
                    return {
                        "status": "failed",
                        "details": "攻击执行完成，但未达到成功指标",
                        "output": output
                    }
            else:
                return {
                    "status": "error",
                    "details": "攻击执行出错",
                    "error": result.stderr
                }
        else:
            return {
                "status": "error",
                "details": "未找到有效的攻击代码"
            }
    except Exception as e:
        print(f"执行攻击时出错: {e}")
        return {"status": "error", "details": str(e)}


def save_attack_result(target: str, attack_path: Dict[str, Any], execution_result: Dict[str, Any]) -> bool:
    """保存攻击结果到数据库"""
    try:
        db = get_db_connection()
        cursor = db.cursor()
        
        # 创建攻击结果表(如果不存在)
        create_table = """
        CREATE TABLE IF NOT EXISTS attack_results (
            id INT AUTO_INCREMENT PRIMARY KEY,
            target VARCHAR(255) NOT NULL,
            attack_name VARCHAR(255) NOT NULL,
            attack_path JSON,
            exploit_code TEXT,
            execution_result JSON,
            status VARCHAR(50) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY target_attack (target, attack_name)
        )"""
        cursor.execute(create_table)
        
        # 提取攻击信息
        attack_name = attack_path.get('attack_path', {}).get('attack_paths', [{}])[0].get('name', 'Unknown Attack')
        exploit_code = attack_path.get('attack_path', {}).get('exploit_code', {}).get('code', '')
        
        # 插入或更新结果
        query = """
        INSERT INTO attack_results 
        (target, attack_name, attack_path, exploit_code, execution_result, status)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE 
            attack_path = VALUES(attack_path),
            exploit_code = VALUES(exploit_code),
            execution_result = VALUES(execution_result),
            status = VALUES(status),
            created_at = CURRENT_TIMESTAMP
        """
        cursor.execute(
            query, 
            (
                target, 
                attack_name, 
                json.dumps(attack_path.get('attack_path', {})), 
                exploit_code,
                json.dumps(execution_result),
                execution_result.get('status', 'unknown')
            )
        )
        db.commit()
        
        cursor.close()
        db.close()
        print(f"成功保存攻击结果: {target} - {attack_name}")
        return True
    except Exception as e:
        print(f"保存攻击结果时出错: {e}")
        return False


def main():
    """主函数"""
    print("启动信息集中器...")
    
    # 获取待扫描的IP段
    ip_segments = get_target_ips()
    if not ip_segments:
        print("没有找到待扫描的IP段，尝试获取新的IP段...")
        ip_segment = get_ip_segments()
        if ip_segment:
            ip_segments = [ip_segment]
    
    print(f"找到{len(ip_segments)}个待扫描的IP段")
    
    # 处理每个IP段
    for ip_segment in ip_segments:
        print(f"\n处理IP段: {ip_segment}")
        
        # 更新IP段状态为'scanning'
        update_ip_segment_status(ip_segment, 'scanning')
        
        try:
            # 收集网络信息
            network_info = collect_network_info(ip_segment)
            
            # 对每个活跃主机收集Web信息并进行分析
            for host in network_info.get('hosts', []):
                if host.get('status') == 'up':
                    ip = host.get('ip')
                    print(f"\n处理活跃主机: {ip}")
                    
                    # 收集Web信息
                    web_info = collect_web_info(ip)
                    
                    # 分析安全信息
                    analysis_result = analyze_security_info(host, web_info)
                    
                    if analysis_result.get('status') == 'success':
                        # 生成攻击路径
                        attack_path = generate_attack_path(analysis_result)
                        
                        if attack_path.get('status') == 'success':
                            # 执行攻击
                            execution_result = execute_attack(attack_path)
                            
                            # 保存攻击结果
                            save_attack_result(ip, attack_path, execution_result)
                            
                            # 如果攻击失败，尝试其他攻击路径
                            if execution_result.get('status') != 'success':
                                print(f"攻击失败，尝试生成其他攻击路径...")
                                # 这里可以添加重试逻辑，例如调整提示词重新生成攻击路径
                        else:
                            print(f"生成攻击路径失败: {attack_path.get('details')}")
                    else:
                        print(f"分析安全信息失败: {analysis_result.get('details')}")
            
            # 更新IP段状态为'done'
            update_ip_segment_status(ip_segment, 'done')
            
        except Exception as e:
            print(f"处理IP段{ip_segment}时出错: {e}")
            # 更新IP段状态为'error'
            update_ip_segment_status(ip_segment, 'error')
    
    print("\n信息集中器任务完成")


if __name__ == "__main__":
    main()