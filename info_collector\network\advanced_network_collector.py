"""
高级网络信息收集器

缺失功能：
1. 网络拓扑发现 - traceroute、路由分析
2. 物联网设备识别 - IoT协议扫描
3. 网络设备指纹识别 - 交换机、路由器、防火墙
4. 网络环境识别 - CDN、负载均衡、云服务
5. 协议深度分析 - 自定义协议识别
6. MAC地址厂商识别
7. 网络性能分析 - 延迟、带宽、丢包率

作者：AI渗透测试系统
版本：1.0.0
"""

import os
import sys
import json
import time
import socket
import struct
import subprocess
import threading
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
import logging
import requests
import ipaddress

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

logger = logging.getLogger(__name__)


class NetworkTopologyDiscoverer:
    """网络拓扑发现器"""
    
    def __init__(self):
        self.max_hops = 30
        self.timeout = 5
        
    def traceroute(self, target: str) -> List[Dict[str, Any]]:
        """
        执行traceroute追踪网络路径
        
        Args:
            target: 目标IP或域名
            
        Returns:
            List[Dict]: 路由跳数信息
        """
        logger.info(f"开始traceroute追踪：{target}")
        route_info = []
        
        try:
            # Windows系统使用tracert
            if os.name == 'nt':
                cmd = f"tracert -h {self.max_hops} -w {self.timeout * 1000} {target}"
            else:
                cmd = f"traceroute -m {self.max_hops} -w {self.timeout} {target}"
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                route_info = self._parse_traceroute_output(result.stdout)
            else:
                logger.error(f"Traceroute失败：{result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error("Traceroute超时")
        except Exception as e:
            logger.error(f"Traceroute执行失败：{e}")
        
        return route_info
    
    def _parse_traceroute_output(self, output: str) -> List[Dict[str, Any]]:
        """解析traceroute输出"""
        route_info = []
        lines = output.strip().split('\n')
        
        for line in lines:
            if line.strip() and not line.startswith('Tracing') and not line.startswith('over'):
                hop_info = self._parse_hop_line(line)
                if hop_info:
                    route_info.append(hop_info)
        
        return route_info
    
    def _parse_hop_line(self, line: str) -> Optional[Dict[str, Any]]:
        """解析单个跳数行"""
        try:
            parts = line.strip().split()
            if len(parts) >= 2:
                hop_num = int(parts[0])
                
                # 提取IP地址和响应时间
                ip_address = None
                hostname = None
                response_times = []
                
                for part in parts[1:]:
                    if part.count('.') == 3:  # IPv4地址
                        try:
                            ipaddress.ip_address(part)
                            ip_address = part
                        except:
                            pass
                    elif part.endswith('ms'):
                        try:
                            response_times.append(float(part[:-2]))
                        except:
                            pass
                    elif not part.startswith('[') and not part.endswith(']'):
                        hostname = part
                
                return {
                    'hop': hop_num,
                    'ip_address': ip_address,
                    'hostname': hostname,
                    'response_times': response_times,
                    'avg_response_time': sum(response_times) / len(response_times) if response_times else None
                }
        except Exception as e:
            logger.debug(f"解析跳数行失败：{line} - {e}")
        
        return None
    
    def discover_network_devices(self, network: str) -> List[Dict[str, Any]]:
        """
        发现网络设备
        
        Args:
            network: 网络段，如 ***********/24
            
        Returns:
            List[Dict]: 网络设备信息
        """
        logger.info(f"开始发现网络设备：{network}")
        devices = []
        
        try:
            network_obj = ipaddress.ip_network(network, strict=False)
            
            # 并发扫描网络中的设备
            with ThreadPoolExecutor(max_workers=50) as executor:
                futures = []
                for ip in network_obj.hosts():
                    future = executor.submit(self._identify_network_device, str(ip))
                    futures.append(future)
                
                for future in futures:
                    try:
                        device_info = future.result(timeout=10)
                        if device_info:
                            devices.append(device_info)
                    except Exception as e:
                        logger.debug(f"设备识别失败：{e}")
        
        except Exception as e:
            logger.error(f"网络设备发现失败：{e}")
        
        return devices
    
    def _identify_network_device(self, ip: str) -> Optional[Dict[str, Any]]:
        """识别单个网络设备"""
        try:
            # 检查主机是否存活
            if not self._ping_host(ip):
                return None
            
            device_info = {
                'ip_address': ip,
                'device_type': 'unknown',
                'vendor': None,
                'model': None,
                'services': [],
                'mac_address': None,
                'hostname': None
            }
            
            # 获取主机名
            try:
                hostname = socket.gethostbyaddr(ip)[0]
                device_info['hostname'] = hostname
            except:
                pass
            
            # 检查常见网络设备端口
            network_device_ports = {
                22: 'SSH',
                23: 'Telnet',
                80: 'HTTP',
                443: 'HTTPS',
                161: 'SNMP',
                162: 'SNMP-Trap',
                8080: 'HTTP-Alt',
                8443: 'HTTPS-Alt'
            }
            
            open_ports = []
            for port, service in network_device_ports.items():
                if self._check_port(ip, port):
                    open_ports.append({'port': port, 'service': service})
            
            device_info['services'] = open_ports
            
            # 基于开放端口推断设备类型
            if any(p['port'] == 161 for p in open_ports):
                device_info['device_type'] = 'network_device'
            
            return device_info if open_ports else None
            
        except Exception as e:
            logger.debug(f"设备识别失败 {ip}：{e}")
            return None
    
    def _ping_host(self, ip: str) -> bool:
        """检查主机是否存活"""
        try:
            if os.name == 'nt':
                cmd = f"ping -n 1 -w 1000 {ip}"
            else:
                cmd = f"ping -c 1 -W 1 {ip}"
            
            result = subprocess.run(cmd, shell=True, capture_output=True, timeout=3)
            return result.returncode == 0
        except:
            return False
    
    def _check_port(self, ip: str, port: int) -> bool:
        """检查端口是否开放"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except:
            return False


class IoTDeviceScanner:
    """物联网设备扫描器"""
    
    def __init__(self):
        self.iot_protocols = {
            'CoAP': 5683,
            'MQTT': 1883,
            'MQTT-TLS': 8883,
            'Modbus': 502,
            'BACnet': 47808,
            'DNP3': 20000,
            'EtherNet/IP': 44818,
            'OPC-UA': 4840
        }
    
    def scan_iot_devices(self, network: str) -> List[Dict[str, Any]]:
        """
        扫描物联网设备
        
        Args:
            network: 网络段
            
        Returns:
            List[Dict]: IoT设备信息
        """
        logger.info(f"开始扫描IoT设备：{network}")
        iot_devices = []
        
        try:
            network_obj = ipaddress.ip_network(network, strict=False)
            
            with ThreadPoolExecutor(max_workers=20) as executor:
                futures = []
                for ip in network_obj.hosts():
                    future = executor.submit(self._scan_iot_protocols, str(ip))
                    futures.append(future)
                
                for future in futures:
                    try:
                        device_info = future.result(timeout=15)
                        if device_info:
                            iot_devices.append(device_info)
                    except Exception as e:
                        logger.debug(f"IoT设备扫描失败：{e}")
        
        except Exception as e:
            logger.error(f"IoT设备扫描失败：{e}")
        
        return iot_devices
    
    def _scan_iot_protocols(self, ip: str) -> Optional[Dict[str, Any]]:
        """扫描单个IP的IoT协议"""
        device_info = {
            'ip_address': ip,
            'device_type': 'iot_device',
            'protocols': [],
            'services': [],
            'fingerprint': {}
        }
        
        found_protocols = []
        
        for protocol, port in self.iot_protocols.items():
            if self._check_iot_protocol(ip, port, protocol):
                found_protocols.append({
                    'protocol': protocol,
                    'port': port,
                    'status': 'open'
                })
        
        if found_protocols:
            device_info['protocols'] = found_protocols
            return device_info
        
        return None
    
    def _check_iot_protocol(self, ip: str, port: int, protocol: str) -> bool:
        """检查特定IoT协议"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((ip, port))
            
            if result == 0:
                # 发送协议特定的探测包
                if protocol == 'CoAP':
                    return self._probe_coap(sock)
                elif protocol == 'MQTT':
                    return self._probe_mqtt(sock)
                elif protocol == 'Modbus':
                    return self._probe_modbus(sock)
                else:
                    return True  # 端口开放即认为支持该协议
            
            sock.close()
            return False
            
        except Exception as e:
            logger.debug(f"IoT协议检查失败 {ip}:{port} - {e}")
            return False
    
    def _probe_coap(self, sock) -> bool:
        """探测CoAP协议"""
        try:
            # CoAP GET请求
            coap_request = b'\x40\x01\x00\x01\xb3.well-known\x04core'
            sock.send(coap_request)
            response = sock.recv(1024)
            return len(response) > 0 and response[0] & 0xC0 == 0x40
        except:
            return False
    
    def _probe_mqtt(self, sock) -> bool:
        """探测MQTT协议"""
        try:
            # MQTT CONNECT包
            mqtt_connect = b'\x10\x0c\x00\x04MQTT\x04\x00\x00\x00\x00\x00'
            sock.send(mqtt_connect)
            response = sock.recv(1024)
            return len(response) > 0 and response[0] == 0x20
        except:
            return False
    
    def _probe_modbus(self, sock) -> bool:
        """探测Modbus协议"""
        try:
            # Modbus读保持寄存器请求
            modbus_request = b'\x00\x01\x00\x00\x00\x06\x01\x03\x00\x00\x00\x01'
            sock.send(modbus_request)
            response = sock.recv(1024)
            return len(response) > 0 and len(response) >= 9
        except:
            return False


class CloudServiceDetector:
    """云服务检测器"""
    
    def __init__(self):
        self.cloud_indicators = {
            'AWS': {
                'ip_ranges': ['52.', '54.', '3.', '13.', '18.'],
                'domains': ['amazonaws.com', 'aws.amazon.com'],
                'headers': ['Server: AmazonS3', 'x-amz-']
            },
            'Azure': {
                'ip_ranges': ['40.', '52.', '13.', '20.'],
                'domains': ['azure.com', 'azurewebsites.net', 'blob.core.windows.net'],
                'headers': ['Server: Microsoft-IIS', 'x-ms-']
            },
            'Alibaba Cloud': {
                'ip_ranges': ['47.', '116.', '120.', '121.'],
                'domains': ['aliyun.com', 'aliyuncs.com'],
                'headers': ['Server: Tengine', 'x-acs-']
            },
            'Google Cloud': {
                'ip_ranges': ['35.', '34.', '104.', '130.'],
                'domains': ['googleapis.com', 'googleusercontent.com'],
                'headers': ['Server: gws', 'x-goog-']
            }
        }
    
    def detect_cloud_service(self, target: str) -> Dict[str, Any]:
        """
        检测云服务
        
        Args:
            target: 目标IP或域名
            
        Returns:
            Dict: 云服务信息
        """
        logger.info(f"开始检测云服务：{target}")
        
        cloud_info = {
            'target': target,
            'cloud_provider': None,
            'services': [],
            'confidence': 0,
            'indicators': []
        }
        
        try:
            # IP地址检测
            if self._is_ip_address(target):
                cloud_info.update(self._detect_by_ip(target))
            
            # 域名检测
            cloud_info.update(self._detect_by_domain(target))
            
            # HTTP头检测
            cloud_info.update(self._detect_by_http_headers(target))
            
            # CDN检测
            cdn_info = self._detect_cdn(target)
            if cdn_info:
                cloud_info['cdn'] = cdn_info
            
        except Exception as e:
            logger.error(f"云服务检测失败：{e}")
            cloud_info['error'] = str(e)
        
        return cloud_info
    
    def _is_ip_address(self, target: str) -> bool:
        """检查是否为IP地址"""
        try:
            ipaddress.ip_address(target)
            return True
        except:
            return False
    
    def _detect_by_ip(self, ip: str) -> Dict[str, Any]:
        """基于IP地址检测云服务"""
        for provider, indicators in self.cloud_indicators.items():
            for ip_prefix in indicators['ip_ranges']:
                if ip.startswith(ip_prefix):
                    return {
                        'cloud_provider': provider,
                        'confidence': 70,
                        'indicators': [f'IP prefix match: {ip_prefix}']
                    }
        return {}
    
    def _detect_by_domain(self, target: str) -> Dict[str, Any]:
        """基于域名检测云服务"""
        for provider, indicators in self.cloud_indicators.items():
            for domain in indicators['domains']:
                if domain in target:
                    return {
                        'cloud_provider': provider,
                        'confidence': 90,
                        'indicators': [f'Domain match: {domain}']
                    }
        return {}
    
    def _detect_by_http_headers(self, target: str) -> Dict[str, Any]:
        """基于HTTP头检测云服务"""
        try:
            # 尝试HTTP和HTTPS
            for protocol in ['http', 'https']:
                try:
                    url = f"{protocol}://{target}"
                    response = requests.head(url, timeout=10, verify=False)
                    
                    headers_str = str(response.headers)
                    
                    for provider, indicators in self.cloud_indicators.items():
                        for header_indicator in indicators['headers']:
                            if header_indicator.lower() in headers_str.lower():
                                return {
                                    'cloud_provider': provider,
                                    'confidence': 80,
                                    'indicators': [f'HTTP header match: {header_indicator}']
                                }
                    break  # 如果HTTP成功就不尝试HTTPS
                except:
                    continue
        except Exception as e:
            logger.debug(f"HTTP头检测失败：{e}")
        
        return {}
    
    def _detect_cdn(self, target: str) -> Optional[Dict[str, Any]]:
        """检测CDN服务"""
        cdn_indicators = {
            'Cloudflare': ['cloudflare', 'cf-ray'],
            'Akamai': ['akamai', 'akamaized'],
            'AWS CloudFront': ['cloudfront', 'x-amz-cf-id'],
            'Azure CDN': ['azureedge', 'x-ms-'],
            'Alibaba CDN': ['alicdn', 'x-acs-'],
            'Fastly': ['fastly', 'x-served-by'],
            'KeyCDN': ['keycdn', 'x-edge-'],
            'MaxCDN': ['maxcdn', 'x-cache']
        }
        
        try:
            for protocol in ['http', 'https']:
                try:
                    url = f"{protocol}://{target}"
                    response = requests.head(url, timeout=10, verify=False)
                    
                    headers_str = str(response.headers).lower()
                    
                    for cdn_name, indicators in cdn_indicators.items():
                        for indicator in indicators:
                            if indicator.lower() in headers_str:
                                return {
                                    'cdn_provider': cdn_name,
                                    'indicator': indicator,
                                    'confidence': 85
                                }
                    break
                except:
                    continue
        except Exception as e:
            logger.debug(f"CDN检测失败：{e}")
        
        return None


class AdvancedNetworkCollector:
    """高级网络信息收集器"""
    
    def __init__(self):
        self.topology_discoverer = NetworkTopologyDiscoverer()
        self.iot_scanner = IoTDeviceScanner()
        self.cloud_detector = CloudServiceDetector()
    
    def collect_advanced_network_info(self, target: str) -> Dict[str, Any]:
        """
        收集高级网络信息
        
        Args:
            target: 目标IP、域名或网络段
            
        Returns:
            Dict: 高级网络信息
        """
        logger.info(f"开始收集高级网络信息：{target}")
        
        advanced_info = {
            'target': target,
            'scan_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'network_topology': {},
            'iot_devices': [],
            'network_devices': [],
            'cloud_services': {},
            'performance_metrics': {}
        }
        
        try:
            # 网络拓扑发现
            if not target.endswith('/24'):  # 单个目标
                advanced_info['network_topology'] = {
                    'route_trace': self.topology_discoverer.traceroute(target)
                }
                
                # 云服务检测
                advanced_info['cloud_services'] = self.cloud_detector.detect_cloud_service(target)
            
            # 如果是网络段，进行设备发现
            if '/' in target:
                advanced_info['network_devices'] = self.topology_discoverer.discover_network_devices(target)
                advanced_info['iot_devices'] = self.iot_scanner.scan_iot_devices(target)
            
        except Exception as e:
            logger.error(f"高级网络信息收集失败：{e}")
            advanced_info['error'] = str(e)
        
        return advanced_info


# 使用示例
if __name__ == "__main__":
    collector = AdvancedNetworkCollector()
    
    # 测试单个目标
    result = collector.collect_advanced_network_info("8.8.8.8")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # 测试网络段
    # result = collector.collect_advanced_network_info("***********/24")
    # print(json.dumps(result, indent=2, ensure_ascii=False))
