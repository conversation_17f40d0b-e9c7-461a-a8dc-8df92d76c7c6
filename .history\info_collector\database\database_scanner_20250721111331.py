"""
数据库指纹识别和漏洞检测器

功能：
1. 数据库服务识别 - MySQL、PostgreSQL、Oracle、SQL Server、MongoDB等
2. 数据库版本检测 - 精确版本识别、补丁级别
3. 数据库配置检查 - 默认配置、弱密码、权限设置
4. 数据库漏洞检测 - 已知CVE、配置缺陷、注入点
5. 数据库枚举 - 数据库列表、表结构、用户权限
6. 数据库安全评估 - 加密状态、审计配置、备份安全
7. NoSQL数据库检测 - MongoDB、Redis、Elasticsearch等

作者：AI渗透测试系统
版本：1.0.0
"""

import socket
import struct
import hashlib
import base64
import json
import time
import re
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
# 可选依赖，如果没有安装则跳过相关功能
try:
    import pymysql
    PYMYSQL_AVAILABLE = True
except ImportError:
    PYMYSQL_AVAILABLE = False
    print("警告：pymysql未安装，MySQL扫描功能将受限")

try:
    import psycopg2
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False
    print("警告：psycopg2未安装，PostgreSQL扫描功能将受限")

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    print("警告：redis未安装，Redis扫描功能将受限")

import requests
from urllib.parse import quote
import ssl

logger = logging.getLogger(__name__)


class DatabaseScanner:
    """数据库扫描器主类"""
    
    def __init__(self):
        # 常见数据库端口
        self.database_ports = {
            'MySQL': [3306, 3307],
            'PostgreSQL': [5432, 5433],
            'Oracle': [1521, 1522, 1526],
            'SQL Server': [1433, 1434],
            'MongoDB': [27017, 27018, 27019],
            'Redis': [6379, 6380],
            'Elasticsearch': [9200, 9300],
            'CouchDB': [5984, 5986],
            'Cassandra': [9042, 9160],
            'InfluxDB': [8086, 8088],
            'Neo4j': [7474, 7687],
            'MemcacheD': [11211],
            'RethinkDB': [28015, 29015],
            'ArangoDB': [8529]
        }
        
        # 数据库指纹特征
        self.database_fingerprints = {
            'MySQL': {
                'banners': [b'mysql_native_password', b'caching_sha2_password'],
                'error_messages': ['mysql', 'MariaDB'],
                'default_databases': ['information_schema', 'mysql', 'performance_schema']
            },
            'PostgreSQL': {
                'banners': [b'SCRAM-SHA-256', b'md5'],
                'error_messages': ['PostgreSQL', 'psql'],
                'default_databases': ['postgres', 'template0', 'template1']
            },
            'Oracle': {
                'banners': [b'DESCRIPTION=', b'TNS'],
                'error_messages': ['ORA-', 'Oracle'],
                'default_databases': ['ORCL', 'XE']
            },
            'SQL Server': {
                'banners': [b'MSSQL', b'Microsoft SQL Server'],
                'error_messages': ['Microsoft SQL Server', 'MSSQL'],
                'default_databases': ['master', 'model', 'msdb', 'tempdb']
            },
            'MongoDB': {
                'banners': [b'MongoDB', b'ismaster'],
                'error_messages': ['MongoDB', 'mongo'],
                'default_databases': ['admin', 'local', 'config']
            },
            'Redis': {
                'banners': [b'PONG', b'redis_version'],
                'error_messages': ['Redis', 'WRONGTYPE'],
                'commands': ['INFO', 'PING']
            }
        }
        
        # 常见弱密码
        self.weak_passwords = [
            '', 'password', '123456', 'admin', 'root', 'sa', 'postgres',
            'mysql', 'oracle', 'test', 'guest', 'user', 'demo',
            'password123', 'admin123', 'root123', '12345678'
        ]
    
    def scan_database_services(self, target: str, ports: List[int] = None) -> Dict[str, Any]:
        """
        扫描数据库服务
        
        Args:
            target: 目标IP或域名
            ports: 指定端口列表，如果为None则扫描所有常见数据库端口
            
        Returns:
            Dict: 数据库扫描结果
        """
        logger.info(f"开始扫描数据库服务：{target}")
        
        scan_results = {
            'target': target,
            'scan_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'discovered_databases': [],
            'vulnerabilities': [],
            'security_issues': [],
            'recommendations': []
        }
        
        # 确定要扫描的端口
        if ports is None:
            ports = []
            for db_ports in self.database_ports.values():
                ports.extend(db_ports)
        
        try:
            # 并发扫描端口
            with ThreadPoolExecutor(max_workers=20) as executor:
                futures = []
                for port in ports:
                    future = executor.submit(self._scan_database_port, target, port)
                    futures.append(future)
                
                for future in as_completed(futures):
                    try:
                        result = future.result(timeout=30)
                        if result:
                            scan_results['discovered_databases'].append(result)
                    except Exception as e:
                        logger.debug(f"数据库端口扫描失败：{e}")
            
            # 对发现的数据库进行深度分析
            for db_info in scan_results['discovered_databases']:
                try:
                    # 漏洞检测
                    vulns = self._detect_database_vulnerabilities(target, db_info)
                    scan_results['vulnerabilities'].extend(vulns)
                    
                    # 安全配置检查
                    security_issues = self._check_database_security(target, db_info)
                    scan_results['security_issues'].extend(security_issues)
                    
                except Exception as e:
                    logger.error(f"数据库深度分析失败：{e}")
            
            # 生成安全建议
            scan_results['recommendations'] = self._generate_database_recommendations(scan_results)
            
        except Exception as e:
            logger.error(f"数据库扫描失败：{e}")
            scan_results['error'] = str(e)
        
        return scan_results
    
    def _scan_database_port(self, target: str, port: int) -> Optional[Dict[str, Any]]:
        """扫描单个数据库端口"""
        try:
            # 检查端口是否开放
            if not self._check_port_open(target, port):
                return None
            
            # 识别数据库类型
            db_type = self._identify_database_type(target, port)
            if not db_type:
                return None
            
            db_info = {
                'type': db_type,
                'host': target,
                'port': port,
                'version': None,
                'banner': None,
                'authentication': {},
                'databases': [],
                'users': [],
                'configuration': {},
                'ssl_enabled': False
            }
            
            # 获取详细信息
            if db_type == 'MySQL':
                db_info.update(self._scan_mysql(target, port))
            elif db_type == 'PostgreSQL':
                db_info.update(self._scan_postgresql(target, port))
            elif db_type == 'MongoDB':
                db_info.update(self._scan_mongodb(target, port))
            elif db_type == 'Redis':
                db_info.update(self._scan_redis(target, port))
            elif db_type == 'Elasticsearch':
                db_info.update(self._scan_elasticsearch(target, port))
            elif db_type == 'Oracle':
                db_info.update(self._scan_oracle(target, port))
            elif db_type == 'SQL Server':
                db_info.update(self._scan_sqlserver(target, port))
            
            return db_info
            
        except Exception as e:
            logger.debug(f"数据库端口扫描失败 {target}:{port} - {e}")
            return None
    
    def _check_port_open(self, target: str, port: int, timeout: int = 5) -> bool:
        """检查端口是否开放"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((target, port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def _identify_database_type(self, target: str, port: int) -> Optional[str]:
        """识别数据库类型"""
        try:
            # 基于端口的初步判断
            for db_type, ports in self.database_ports.items():
                if port in ports:
                    # 进一步验证
                    if self._verify_database_type(target, port, db_type):
                        return db_type
            
            # 如果端口不在常见列表中，尝试指纹识别
            return self._fingerprint_database(target, port)
            
        except Exception as e:
            logger.debug(f"数据库类型识别失败：{e}")
            return None
    
    def _verify_database_type(self, target: str, port: int, db_type: str) -> bool:
        """验证数据库类型"""
        try:
            if db_type == 'MySQL':
                return self._verify_mysql(target, port)
            elif db_type == 'PostgreSQL':
                return self._verify_postgresql(target, port)
            elif db_type == 'MongoDB':
                return self._verify_mongodb(target, port)
            elif db_type == 'Redis':
                return self._verify_redis(target, port)
            elif db_type == 'Elasticsearch':
                return self._verify_elasticsearch(target, port)
            elif db_type == 'Oracle':
                return self._verify_oracle(target, port)
            elif db_type == 'SQL Server':
                return self._verify_sqlserver(target, port)
            
            return False
            
        except Exception:
            return False
    
    def _verify_mysql(self, target: str, port: int) -> bool:
        """验证MySQL服务"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((target, port))
            
            # 接收MySQL握手包
            data = sock.recv(1024)
            sock.close()
            
            # 检查MySQL握手包特征
            if len(data) > 4:
                # MySQL握手包以协议版本开始
                protocol_version = data[4]
                if protocol_version == 10:  # MySQL协议版本10
                    return True
                
                # 检查是否包含MySQL特征字符串
                if b'mysql_native_password' in data or b'caching_sha2_password' in data:
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _verify_postgresql(self, target: str, port: int) -> bool:
        """验证PostgreSQL服务"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((target, port))
            
            # 发送PostgreSQL启动消息
            startup_message = struct.pack('>I', 8) + struct.pack('>I', 196608)  # 协议版本3.0
            sock.send(startup_message)
            
            # 接收响应
            data = sock.recv(1024)
            sock.close()
            
            # 检查PostgreSQL响应特征
            if len(data) > 0:
                # PostgreSQL错误响应通常以'E'开始
                if data[0:1] == b'E' or data[0:1] == b'R':
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _verify_mongodb(self, target: str, port: int) -> bool:
        """验证MongoDB服务"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((target, port))
            
            # 发送MongoDB ismaster命令
            ismaster_cmd = {
                "ismaster": 1
            }
            
            # 构造MongoDB消息
            message = self._build_mongodb_message(ismaster_cmd)
            sock.send(message)
            
            # 接收响应
            data = sock.recv(1024)
            sock.close()
            
            # 检查MongoDB响应
            if len(data) > 16:  # MongoDB响应头至少16字节
                return True
            
            return False
            
        except Exception:
            return False
    
    def _verify_redis(self, target: str, port: int) -> bool:
        """验证Redis服务"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((target, port))
            
            # 发送PING命令
            sock.send(b'PING\r\n')
            
            # 接收响应
            data = sock.recv(1024)
            sock.close()
            
            # 检查Redis PONG响应
            if b'PONG' in data:
                return True
            
            return False
            
        except Exception:
            return False
    
    def _verify_elasticsearch(self, target: str, port: int) -> bool:
        """验证Elasticsearch服务"""
        try:
            # Elasticsearch通常使用HTTP协议
            url = f"http://{target}:{port}/"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if 'cluster_name' in data and 'version' in data:
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _verify_oracle(self, target: str, port: int) -> bool:
        """验证Oracle服务"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((target, port))
            
            # 发送Oracle TNS连接请求
            tns_connect = (
                b'\x00\x3a\x00\x00\x01\x00\x00\x00'
                b'\x01\x36\x01\x2c\x00\x00\x08\x00'
                b'\x7f\xff\x7f\x08\x00\x00\x00\x01'
                b'\x00\x1d\x00\x3a\x00\x00\x00\x00'
                b'\x00\x00\x00\x00\x00\x00\x00\x00'
                b'\x00\x00\x00\x00\x34\xe6\x00\x00'
                b'\x00\x01\x00\x00\x00\x00\x00\x00'
                b'\x00\x00'
            )
            
            sock.send(tns_connect)
            
            # 接收响应
            data = sock.recv(1024)
            sock.close()
            
            # 检查Oracle TNS响应
            if len(data) > 8 and data[4:8] == b'\x00\x00\x01\x00':
                return True
            
            return False
            
        except Exception:
            return False
    
    def _verify_sqlserver(self, target: str, port: int) -> bool:
        """验证SQL Server服务"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((target, port))
            
            # 发送SQL Server预登录包
            prelogin_packet = (
                b'\x12\x01\x00\x34\x00\x00\x00\x00'
                b'\x00\x00\x15\x00\x06\x01\x00\x1b'
                b'\x00\x01\x02\x00\x1c\x00\x01\x03'
                b'\x00\x1d\x00\x00\xff\x09\x00\x00'
                b'\x00\x00\x00\x00\x00\x00\x01\x00'
                b'\x00\x00\x02\x00\x00\x00'
            )
            
            sock.send(prelogin_packet)
            
            # 接收响应
            data = sock.recv(1024)
            sock.close()
            
            # 检查SQL Server响应
            if len(data) > 8 and data[0] == 0x04:  # SQL Server响应包类型
                return True
            
            return False
            
        except Exception:
            return False
    
    def _fingerprint_database(self, target: str, port: int) -> Optional[str]:
        """数据库指纹识别"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((target, port))
            
            # 接收banner
            data = sock.recv(1024)
            sock.close()
            
            # 基于banner特征识别
            for db_type, fingerprints in self.database_fingerprints.items():
                for banner in fingerprints.get('banners', []):
                    if banner in data:
                        return db_type
            
            return None
            
        except Exception:
            return None
    
    def _build_mongodb_message(self, command: Dict) -> bytes:
        """构造MongoDB消息"""
        try:
            import bson
            
            # 序列化命令
            command_bson = bson.encode(command)
            
            # 构造消息头
            message_length = 16 + len(command_bson)  # 头部16字节 + 命令长度
            request_id = 1
            response_to = 0
            opcode = 2004  # OP_QUERY
            
            header = struct.pack('<iiii', message_length, request_id, response_to, opcode)
            
            # 构造查询标志和集合名
            flags = 0
            collection_name = b'admin.$cmd\x00'
            skip = 0
            limit = 1
            
            query_part = struct.pack('<i', flags) + collection_name + struct.pack('<ii', skip, limit)
            
            return header + query_part + command_bson
            
        except Exception:
            # 如果bson不可用，返回简单的测试消息
            return b'\x3f\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd4\x07\x00\x00\x00\x00\x00\x00admin.$cmd\x00\x00\x00\x00\x00\x01\x00\x00\x00\x1b\x00\x00\x00\x10ismaster\x00\x01\x00\x00\x00\x00'
    
    def _scan_mysql(self, target: str, port: int) -> Dict[str, Any]:
        """扫描MySQL数据库"""
        mysql_info = {
            'version': None,
            'banner': None,
            'authentication': {},
            'databases': [],
            'users': [],
            'configuration': {},
            'ssl_enabled': False
        }
        
        try:
            # 获取版本信息
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((target, port))
            
            # 接收握手包
            handshake = sock.recv(1024)
            sock.close()
            
            if len(handshake) > 5:
                # 解析版本信息
                version_end = handshake.find(b'\x00', 5)
                if version_end > 5:
                    version = handshake[5:version_end].decode('utf-8', errors='ignore')
                    mysql_info['version'] = version
                    mysql_info['banner'] = handshake[:100].hex()
            
            # 尝试弱密码连接
            mysql_info['authentication'] = self._test_mysql_authentication(target, port)
            
            # 如果能够连接，获取更多信息
            if mysql_info['authentication'].get('weak_credentials'):
                mysql_info.update(self._enumerate_mysql_details(target, port, mysql_info['authentication']))
            
        except Exception as e:
            logger.debug(f"MySQL扫描失败：{e}")
            mysql_info['error'] = str(e)
        
        return mysql_info
    
    def _test_mysql_authentication(self, target: str, port: int) -> Dict[str, Any]:
        """测试MySQL认证"""
        auth_info = {
            'anonymous_access': False,
            'weak_credentials': [],
            'authentication_methods': []
        }

        if not PYMYSQL_AVAILABLE:
            auth_info['error'] = 'pymysql库未安装，无法进行MySQL认证测试'
            return auth_info

        # 测试匿名访问
        try:
            conn = pymysql.connect(
                host=target,
                port=port,
                user='',
                password='',
                connect_timeout=5
            )
            auth_info['anonymous_access'] = True
            conn.close()
        except:
            pass

        # 测试常见用户名和弱密码
        common_users = ['root', 'admin', 'mysql', 'test', 'user']

        for username in common_users:
            for password in self.weak_passwords:
                try:
                    conn = pymysql.connect(
                        host=target,
                        port=port,
                        user=username,
                        password=password,
                        connect_timeout=5
                    )
                    auth_info['weak_credentials'].append({
                        'username': username,
                        'password': password
                    })
                    conn.close()
                    break  # 找到一个有效密码就跳出
                except:
                    continue

        return auth_info
    
    def _enumerate_mysql_details(self, target: str, port: int, auth_info: Dict) -> Dict[str, Any]:
        """枚举MySQL详细信息"""
        details = {
            'databases': [],
            'users': [],
            'configuration': {},
            'ssl_enabled': False
        }
        
        try:
            # 使用找到的凭据连接
            if auth_info.get('weak_credentials'):
                cred = auth_info['weak_credentials'][0]
                conn = pymysql.connect(
                    host=target,
                    port=port,
                    user=cred['username'],
                    password=cred['password'],
                    connect_timeout=10
                )
                
                cursor = conn.cursor()
                
                # 获取数据库列表
                try:
                    cursor.execute("SHOW DATABASES")
                    databases = cursor.fetchall()
                    details['databases'] = [db[0] for db in databases]
                except:
                    pass
                
                # 获取用户列表
                try:
                    cursor.execute("SELECT user, host FROM mysql.user")
                    users = cursor.fetchall()
                    details['users'] = [{'user': user[0], 'host': user[1]} for user in users]
                except:
                    pass
                
                # 获取配置信息
                try:
                    cursor.execute("SHOW VARIABLES LIKE 'version%'")
                    variables = cursor.fetchall()
                    for var in variables:
                        details['configuration'][var[0]] = var[1]
                except:
                    pass
                
                # 检查SSL状态
                try:
                    cursor.execute("SHOW VARIABLES LIKE 'have_ssl'")
                    ssl_result = cursor.fetchone()
                    if ssl_result and ssl_result[1] == 'YES':
                        details['ssl_enabled'] = True
                except:
                    pass
                
                cursor.close()
                conn.close()
        
        except Exception as e:
            logger.debug(f"MySQL详细信息枚举失败：{e}")
        
        return details
    
    def _scan_postgresql(self, target: str, port: int) -> Dict[str, Any]:
        """扫描PostgreSQL数据库"""
        pg_info = {
            'version': None,
            'banner': None,
            'authentication': {},
            'databases': [],
            'users': [],
            'configuration': {},
            'ssl_enabled': False
        }
        
        try:
            # 测试认证
            pg_info['authentication'] = self._test_postgresql_authentication(target, port)
            
            # 如果能够连接，获取更多信息
            if pg_info['authentication'].get('weak_credentials'):
                pg_info.update(self._enumerate_postgresql_details(target, port, pg_info['authentication']))
        
        except Exception as e:
            logger.debug(f"PostgreSQL扫描失败：{e}")
            pg_info['error'] = str(e)
        
        return pg_info
    
    def _test_postgresql_authentication(self, target: str, port: int) -> Dict[str, Any]:
        """测试PostgreSQL认证"""
        auth_info = {
            'weak_credentials': [],
            'trust_authentication': False
        }
        
        # 测试常见用户名和弱密码
        common_users = ['postgres', 'admin', 'root', 'test', 'user']
        
        for username in common_users:
            for password in self.weak_passwords:
                try:
                    conn = psycopg2.connect(
                        host=target,
                        port=port,
                        user=username,
                        password=password,
                        database='postgres',
                        connect_timeout=5
                    )
                    auth_info['weak_credentials'].append({
                        'username': username,
                        'password': password
                    })
                    conn.close()
                    break
                except:
                    continue
        
        return auth_info
    
    def _enumerate_postgresql_details(self, target: str, port: int, auth_info: Dict) -> Dict[str, Any]:
        """枚举PostgreSQL详细信息"""
        details = {
            'databases': [],
            'users': [],
            'configuration': {},
            'ssl_enabled': False,
            'version': None
        }
        
        try:
            if auth_info.get('weak_credentials'):
                cred = auth_info['weak_credentials'][0]
                conn = psycopg2.connect(
                    host=target,
                    port=port,
                    user=cred['username'],
                    password=cred['password'],
                    database='postgres',
                    connect_timeout=10
                )
                
                cursor = conn.cursor()
                
                # 获取版本信息
                try:
                    cursor.execute("SELECT version()")
                    version = cursor.fetchone()[0]
                    details['version'] = version
                except:
                    pass
                
                # 获取数据库列表
                try:
                    cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false")
                    databases = cursor.fetchall()
                    details['databases'] = [db[0] for db in databases]
                except:
                    pass
                
                # 获取用户列表
                try:
                    cursor.execute("SELECT usename FROM pg_user")
                    users = cursor.fetchall()
                    details['users'] = [user[0] for user in users]
                except:
                    pass
                
                cursor.close()
                conn.close()
        
        except Exception as e:
            logger.debug(f"PostgreSQL详细信息枚举失败：{e}")
        
        return details
    
    def _scan_mongodb(self, target: str, port: int) -> Dict[str, Any]:
        """扫描MongoDB数据库"""
        mongo_info = {
            'version': None,
            'authentication': {},
            'databases': [],
            'collections': {},
            'configuration': {}
        }
        
        try:
            # 测试MongoDB连接
            mongo_info['authentication'] = self._test_mongodb_authentication(target, port)
            
            # 如果能够连接，获取更多信息
            if mongo_info['authentication'].get('no_auth_required'):
                mongo_info.update(self._enumerate_mongodb_details(target, port))
        
        except Exception as e:
            logger.debug(f"MongoDB扫描失败：{e}")
            mongo_info['error'] = str(e)
        
        return mongo_info
    
    def _test_mongodb_authentication(self, target: str, port: int) -> Dict[str, Any]:
        """测试MongoDB认证"""
        auth_info = {
            'no_auth_required': False,
            'weak_credentials': []
        }
        
        try:
            # 尝试无认证连接
            from pymongo import MongoClient
            
            client = MongoClient(target, port, serverSelectionTimeoutMS=5000)
            
            # 尝试列出数据库
            db_list = client.list_database_names()
            auth_info['no_auth_required'] = True
            client.close()
            
        except Exception:
            # 如果无认证失败，尝试弱密码
            pass
        
        return auth_info
    
    def _enumerate_mongodb_details(self, target: str, port: int) -> Dict[str, Any]:
        """枚举MongoDB详细信息"""
        details = {
            'databases': [],
            'collections': {},
            'configuration': {},
            'version': None
        }
        
        try:
            from pymongo import MongoClient
            
            client = MongoClient(target, port, serverSelectionTimeoutMS=10000)
            
            # 获取服务器信息
            try:
                server_info = client.admin.command('buildInfo')
                details['version'] = server_info.get('version')
                details['configuration'] = server_info
            except:
                pass
            
            # 获取数据库列表
            try:
                details['databases'] = client.list_database_names()
            except:
                pass
            
            # 获取每个数据库的集合
            for db_name in details['databases']:
                try:
                    db = client[db_name]
                    details['collections'][db_name] = db.list_collection_names()
                except:
                    pass
            
            client.close()
        
        except Exception as e:
            logger.debug(f"MongoDB详细信息枚举失败：{e}")
        
        return details
    
    def _scan_redis(self, target: str, port: int) -> Dict[str, Any]:
        """扫描Redis数据库"""
        redis_info = {
            'version': None,
            'authentication': {},
            'configuration': {},
            'keyspace': {}
        }
        
        try:
            # 测试Redis连接
            redis_info['authentication'] = self._test_redis_authentication(target, port)
            
            # 如果能够连接，获取更多信息
            if redis_info['authentication'].get('no_auth_required'):
                redis_info.update(self._enumerate_redis_details(target, port))
        
        except Exception as e:
            logger.debug(f"Redis扫描失败：{e}")
            redis_info['error'] = str(e)
        
        return redis_info
    
    def _test_redis_authentication(self, target: str, port: int) -> Dict[str, Any]:
        """测试Redis认证"""
        auth_info = {
            'no_auth_required': False,
            'weak_passwords': []
        }
        
        try:
            # 尝试无密码连接
            r = redis.Redis(host=target, port=port, socket_timeout=5)
            r.ping()
            auth_info['no_auth_required'] = True
            r.close()
            
        except redis.AuthenticationError:
            # 需要密码，尝试弱密码
            for password in self.weak_passwords:
                try:
                    r = redis.Redis(host=target, port=port, password=password, socket_timeout=5)
                    r.ping()
                    auth_info['weak_passwords'].append(password)
                    r.close()
                    break
                except:
                    continue
        except Exception:
            pass
        
        return auth_info
    
    def _enumerate_redis_details(self, target: str, port: int) -> Dict[str, Any]:
        """枚举Redis详细信息"""
        details = {
            'version': None,
            'configuration': {},
            'keyspace': {},
            'memory_usage': None
        }
        
        try:
            r = redis.Redis(host=target, port=port, socket_timeout=10)
            
            # 获取服务器信息
            try:
                info = r.info()
                details['version'] = info.get('redis_version')
                details['configuration'] = info
                details['memory_usage'] = info.get('used_memory_human')
            except:
                pass
            
            # 获取键空间信息
            try:
                keyspace_info = r.info('keyspace')
                details['keyspace'] = keyspace_info
            except:
                pass
            
            r.close()
        
        except Exception as e:
            logger.debug(f"Redis详细信息枚举失败：{e}")
        
        return details
    
    def _scan_elasticsearch(self, target: str, port: int) -> Dict[str, Any]:
        """扫描Elasticsearch"""
        es_info = {
            'version': None,
            'cluster_info': {},
            'indices': [],
            'authentication': {}
        }
        
        try:
            # 尝试访问Elasticsearch API
            url = f"http://{target}:{port}/"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                cluster_info = response.json()
                es_info['cluster_info'] = cluster_info
                es_info['version'] = cluster_info.get('version', {}).get('number')
                
                # 获取索引信息
                try:
                    indices_url = f"http://{target}:{port}/_cat/indices?format=json"
                    indices_response = requests.get(indices_url, timeout=10)
                    if indices_response.status_code == 200:
                        es_info['indices'] = indices_response.json()
                except:
                    pass
                
                es_info['authentication']['no_auth_required'] = True
            
        except Exception as e:
            logger.debug(f"Elasticsearch扫描失败：{e}")
            es_info['error'] = str(e)
        
        return es_info
    
    def _scan_oracle(self, target: str, port: int) -> Dict[str, Any]:
        """扫描Oracle数据库"""
        oracle_info = {
            'version': None,
            'sid': None,
            'authentication': {},
            'configuration': {}
        }
        
        try:
            # Oracle扫描需要专门的库，这里提供基础框架
            oracle_info['authentication'] = self._test_oracle_authentication(target, port)
        
        except Exception as e:
            logger.debug(f"Oracle扫描失败：{e}")
            oracle_info['error'] = str(e)
        
        return oracle_info
    
    def _test_oracle_authentication(self, target: str, port: int) -> Dict[str, Any]:
        """测试Oracle认证"""
        auth_info = {
            'weak_credentials': [],
            'default_accounts': []
        }
        
        # Oracle默认账户
        default_accounts = [
            ('sys', 'sys'),
            ('system', 'manager'),
            ('scott', 'tiger'),
            ('hr', 'hr'),
            ('oe', 'oe')
        ]
        
        # 这里需要使用cx_Oracle库进行实际测试
        # 由于依赖复杂，这里只提供框架
        
        return auth_info
    
    def _scan_sqlserver(self, target: str, port: int) -> Dict[str, Any]:
        """扫描SQL Server数据库"""
        sqlserver_info = {
            'version': None,
            'authentication': {},
            'databases': [],
            'configuration': {}
        }
        
        try:
            sqlserver_info['authentication'] = self._test_sqlserver_authentication(target, port)
        
        except Exception as e:
            logger.debug(f"SQL Server扫描失败：{e}")
            sqlserver_info['error'] = str(e)
        
        return sqlserver_info
    
    def _test_sqlserver_authentication(self, target: str, port: int) -> Dict[str, Any]:
        """测试SQL Server认证"""
        auth_info = {
            'weak_credentials': [],
            'windows_authentication': False
        }
        
        # SQL Server常见账户
        common_accounts = [
            ('sa', ''),
            ('sa', 'sa'),
            ('sa', 'password'),
            ('admin', 'admin')
        ]
        
        # 这里需要使用pyodbc或pymssql库进行实际测试
        # 由于依赖复杂，这里只提供框架
        
        return auth_info

    def _detect_database_vulnerabilities(self, target: str, db_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检测数据库漏洞"""
        vulnerabilities = []

        try:
            db_type = db_info.get('type')
            version = db_info.get('version', '')

            # 检测版本相关漏洞
            version_vulns = self._check_version_vulnerabilities(db_type, version)
            vulnerabilities.extend(version_vulns)

            # 检测配置漏洞
            config_vulns = self._check_configuration_vulnerabilities(db_info)
            vulnerabilities.extend(config_vulns)

            # 检测认证漏洞
            auth_vulns = self._check_authentication_vulnerabilities(db_info)
            vulnerabilities.extend(auth_vulns)

            # 检测权限漏洞
            privilege_vulns = self._check_privilege_vulnerabilities(db_info)
            vulnerabilities.extend(privilege_vulns)

        except Exception as e:
            logger.error(f"数据库漏洞检测失败：{e}")

        return vulnerabilities

    def _check_version_vulnerabilities(self, db_type: str, version: str) -> List[Dict[str, Any]]:
        """检查版本相关漏洞"""
        vulnerabilities = []

        # 数据库版本漏洞库
        version_vulns = {
            'MySQL': {
                '5.7.0-5.7.20': [
                    {
                        'cve': 'CVE-2017-3636',
                        'severity': 'medium',
                        'description': 'MySQL Server Client programs unspecified vulnerability',
                        'affected_versions': '5.7.0 to 5.7.20'
                    }
                ],
                '8.0.0-8.0.15': [
                    {
                        'cve': 'CVE-2019-2534',
                        'severity': 'medium',
                        'description': 'MySQL Server Replication unspecified vulnerability',
                        'affected_versions': '8.0.0 to 8.0.15'
                    }
                ]
            },
            'PostgreSQL': {
                '9.3-11.2': [
                    {
                        'cve': 'CVE-2019-10127',
                        'severity': 'high',
                        'description': 'PostgreSQL memory disclosure vulnerability',
                        'affected_versions': '9.3 to 11.2'
                    }
                ]
            },
            'MongoDB': {
                '3.6.0-4.0.5': [
                    {
                        'cve': 'CVE-2019-2386',
                        'severity': 'medium',
                        'description': 'MongoDB Server Side JavaScript execution vulnerability',
                        'affected_versions': '3.6.0 to 4.0.5'
                    }
                ]
            },
            'Redis': {
                '4.0.0-5.0.3': [
                    {
                        'cve': 'CVE-2018-12326',
                        'severity': 'high',
                        'description': 'Redis buffer overflow vulnerability',
                        'affected_versions': '4.0.0 to 5.0.3'
                    }
                ]
            }
        }

        if db_type in version_vulns and version:
            for version_range, vulns in version_vulns[db_type].items():
                if self._is_version_in_range(version, version_range):
                    vulnerabilities.extend(vulns)

        return vulnerabilities

    def _is_version_in_range(self, version: str, version_range: str) -> bool:
        """检查版本是否在漏洞影响范围内"""
        try:
            # 简化的版本比较逻辑
            if '-' in version_range:
                start_version, end_version = version_range.split('-')
                # 这里需要更复杂的版本比较逻辑
                return True  # 简化实现
            else:
                return version.startswith(version_range)
        except:
            return False

    def _check_configuration_vulnerabilities(self, db_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查配置漏洞"""
        vulnerabilities = []

        db_type = db_info.get('type')
        config = db_info.get('configuration', {})

        if db_type == 'MySQL':
            # 检查MySQL配置漏洞
            if not config.get('ssl_enabled', False):
                vulnerabilities.append({
                    'type': 'configuration',
                    'severity': 'medium',
                    'description': 'SSL/TLS encryption is not enabled',
                    'recommendation': 'Enable SSL/TLS encryption for secure connections'
                })

            # 检查日志配置
            if config.get('general_log') == 'OFF':
                vulnerabilities.append({
                    'type': 'configuration',
                    'severity': 'low',
                    'description': 'General query log is disabled',
                    'recommendation': 'Enable general query log for audit purposes'
                })

        elif db_type == 'PostgreSQL':
            # 检查PostgreSQL配置漏洞
            if not db_info.get('ssl_enabled', False):
                vulnerabilities.append({
                    'type': 'configuration',
                    'severity': 'medium',
                    'description': 'SSL connections are not enforced',
                    'recommendation': 'Configure PostgreSQL to require SSL connections'
                })

        elif db_type == 'MongoDB':
            # 检查MongoDB配置漏洞
            if db_info.get('authentication', {}).get('no_auth_required'):
                vulnerabilities.append({
                    'type': 'configuration',
                    'severity': 'critical',
                    'description': 'MongoDB authentication is disabled',
                    'recommendation': 'Enable authentication and create user accounts'
                })

        elif db_type == 'Redis':
            # 检查Redis配置漏洞
            if db_info.get('authentication', {}).get('no_auth_required'):
                vulnerabilities.append({
                    'type': 'configuration',
                    'severity': 'high',
                    'description': 'Redis authentication is disabled',
                    'recommendation': 'Set a strong password using requirepass directive'
                })

        elif db_type == 'Elasticsearch':
            # 检查Elasticsearch配置漏洞
            if db_info.get('authentication', {}).get('no_auth_required'):
                vulnerabilities.append({
                    'type': 'configuration',
                    'severity': 'critical',
                    'description': 'Elasticsearch has no authentication configured',
                    'recommendation': 'Enable X-Pack security or configure authentication'
                })

        return vulnerabilities

    def _check_authentication_vulnerabilities(self, db_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查认证漏洞"""
        vulnerabilities = []

        auth_info = db_info.get('authentication', {})

        # 检查匿名访问
        if auth_info.get('anonymous_access') or auth_info.get('no_auth_required'):
            vulnerabilities.append({
                'type': 'authentication',
                'severity': 'critical',
                'description': 'Database allows anonymous access without authentication',
                'recommendation': 'Disable anonymous access and require authentication'
            })

        # 检查弱密码
        weak_creds = auth_info.get('weak_credentials', [])
        if weak_creds:
            vulnerabilities.append({
                'type': 'authentication',
                'severity': 'high',
                'description': f'Database has weak credentials: {len(weak_creds)} accounts found',
                'recommendation': 'Change all weak passwords to strong, complex passwords',
                'details': weak_creds
            })

        # 检查默认账户
        default_accounts = auth_info.get('default_accounts', [])
        if default_accounts:
            vulnerabilities.append({
                'type': 'authentication',
                'severity': 'medium',
                'description': f'Default database accounts detected: {len(default_accounts)} accounts',
                'recommendation': 'Remove or secure default database accounts',
                'details': default_accounts
            })

        return vulnerabilities

    def _check_privilege_vulnerabilities(self, db_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查权限漏洞"""
        vulnerabilities = []

        users = db_info.get('users', [])
        db_type = db_info.get('type')

        # 检查超级用户权限
        if db_type == 'MySQL':
            for user in users:
                if isinstance(user, dict) and user.get('user') in ['root', 'admin']:
                    if user.get('host') == '%':  # 允许从任何主机连接
                        vulnerabilities.append({
                            'type': 'privilege',
                            'severity': 'high',
                            'description': f'Superuser {user["user"]} can connect from any host',
                            'recommendation': 'Restrict superuser access to specific hosts only'
                        })

        elif db_type == 'PostgreSQL':
            for user in users:
                if user == 'postgres':
                    vulnerabilities.append({
                        'type': 'privilege',
                        'severity': 'medium',
                        'description': 'Default superuser account "postgres" is active',
                        'recommendation': 'Create dedicated admin accounts and restrict postgres user'
                    })

        return vulnerabilities

    def _check_database_security(self, target: str, db_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查数据库安全配置"""
        security_issues = []

        try:
            db_type = db_info.get('type')

            # 检查网络安全
            network_issues = self._check_network_security(target, db_info)
            security_issues.extend(network_issues)

            # 检查数据加密
            encryption_issues = self._check_data_encryption(db_info)
            security_issues.extend(encryption_issues)

            # 检查审计配置
            audit_issues = self._check_audit_configuration(db_info)
            security_issues.extend(audit_issues)

            # 检查备份安全
            backup_issues = self._check_backup_security(db_info)
            security_issues.extend(backup_issues)

        except Exception as e:
            logger.error(f"数据库安全检查失败：{e}")

        return security_issues

    def _check_network_security(self, target: str, db_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查网络安全配置"""
        issues = []

        port = db_info.get('port')

        # 检查是否绑定到所有接口
        try:
            # 尝试从不同IP连接来检测绑定配置
            if self._check_port_open('0.0.0.0', port):
                issues.append({
                    'type': 'network_security',
                    'severity': 'medium',
                    'description': 'Database may be bound to all network interfaces',
                    'recommendation': 'Bind database only to necessary network interfaces'
                })
        except:
            pass

        # 检查SSL/TLS配置
        if not db_info.get('ssl_enabled', False):
            issues.append({
                'type': 'network_security',
                'severity': 'medium',
                'description': 'Database connections are not encrypted',
                'recommendation': 'Enable SSL/TLS encryption for all database connections'
            })

        return issues

    def _check_data_encryption(self, db_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查数据加密配置"""
        issues = []

        db_type = db_info.get('type')

        # 检查静态数据加密
        if db_type in ['MySQL', 'PostgreSQL', 'SQL Server']:
            # 这里需要查询具体的加密配置
            # 简化实现，假设没有启用加密
            issues.append({
                'type': 'data_encryption',
                'severity': 'medium',
                'description': 'Data-at-rest encryption status unknown or disabled',
                'recommendation': 'Enable transparent data encryption (TDE) for sensitive data'
            })

        return issues

    def _check_audit_configuration(self, db_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查审计配置"""
        issues = []

        db_type = db_info.get('type')
        config = db_info.get('configuration', {})

        if db_type == 'MySQL':
            # 检查审计日志
            if config.get('audit_log_policy') != 'ALL':
                issues.append({
                    'type': 'audit_configuration',
                    'severity': 'low',
                    'description': 'MySQL audit logging is not fully enabled',
                    'recommendation': 'Enable comprehensive audit logging for security monitoring'
                })

        elif db_type == 'PostgreSQL':
            # 检查日志配置
            if not config.get('log_statement'):
                issues.append({
                    'type': 'audit_configuration',
                    'severity': 'low',
                    'description': 'PostgreSQL statement logging is not configured',
                    'recommendation': 'Configure log_statement for audit purposes'
                })

        return issues

    def _check_backup_security(self, db_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查备份安全配置"""
        issues = []

        # 这里需要检查备份文件的安全性
        # 由于无法直接访问文件系统，这里提供框架

        issues.append({
            'type': 'backup_security',
            'severity': 'info',
            'description': 'Backup security configuration should be reviewed',
            'recommendation': 'Ensure database backups are encrypted and stored securely'
        })

        return issues

    def _generate_database_recommendations(self, scan_results: Dict[str, Any]) -> List[str]:
        """生成数据库安全建议"""
        recommendations = []

        # 基于发现的漏洞生成建议
        vulnerabilities = scan_results.get('vulnerabilities', [])
        security_issues = scan_results.get('security_issues', [])

        # 高优先级建议
        critical_vulns = [v for v in vulnerabilities if v.get('severity') == 'critical']
        if critical_vulns:
            recommendations.append("立即修复所有严重级别的数据库漏洞")

        # 认证相关建议
        auth_issues = [v for v in vulnerabilities if v.get('type') == 'authentication']
        if auth_issues:
            recommendations.extend([
                "强化数据库认证机制",
                "更改所有默认和弱密码",
                "禁用不必要的数据库账户"
            ])

        # 配置相关建议
        config_issues = [v for v in vulnerabilities if v.get('type') == 'configuration']
        if config_issues:
            recommendations.extend([
                "审查和加固数据库配置",
                "启用SSL/TLS加密",
                "配置适当的日志和审计"
            ])

        # 网络安全建议
        network_issues = [s for s in security_issues if s.get('type') == 'network_security']
        if network_issues:
            recommendations.extend([
                "限制数据库网络访问",
                "使用防火墙保护数据库端口",
                "实施网络分段"
            ])

        # 通用建议
        recommendations.extend([
            "定期更新数据库软件和补丁",
            "实施最小权限原则",
            "定期备份数据库并测试恢复",
            "监控数据库访问和异常活动"
        ])

        return list(set(recommendations))  # 去重


class DatabaseVulnerabilityScanner:
    """数据库漏洞专项扫描器"""

    def __init__(self):
        self.scanner = DatabaseScanner()

        # SQL注入测试载荷
        self.sql_injection_payloads = [
            "' OR '1'='1",
            "' OR 1=1--",
            "' UNION SELECT NULL--",
            "'; DROP TABLE users--",
            "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--"
        ]

        # NoSQL注入测试载荷
        self.nosql_injection_payloads = [
            {"$ne": None},
            {"$gt": ""},
            {"$where": "this.password.length > 0"},
            {"$regex": ".*"}
        ]

    def scan_sql_injection_vulnerabilities(self, target: str, db_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """扫描SQL注入漏洞"""
        vulnerabilities = []

        db_type = db_info.get('type')
        auth_info = db_info.get('authentication', {})

        # 如果有有效凭据，测试SQL注入
        if auth_info.get('weak_credentials'):
            cred = auth_info['weak_credentials'][0]

            try:
                if db_type == 'MySQL':
                    vulns = self._test_mysql_injection(target, db_info, cred)
                    vulnerabilities.extend(vulns)
                elif db_type == 'PostgreSQL':
                    vulns = self._test_postgresql_injection(target, db_info, cred)
                    vulnerabilities.extend(vulns)

            except Exception as e:
                logger.debug(f"SQL注入测试失败：{e}")

        return vulnerabilities

    def _test_mysql_injection(self, target: str, db_info: Dict[str, Any], cred: Dict[str, str]) -> List[Dict[str, Any]]:
        """测试MySQL SQL注入"""
        vulnerabilities = []

        try:
            conn = pymysql.connect(
                host=target,
                port=db_info.get('port', 3306),
                user=cred['username'],
                password=cred['password'],
                connect_timeout=10
            )

            cursor = conn.cursor()

            # 测试基本SQL注入
            for payload in self.sql_injection_payloads:
                try:
                    # 构造测试查询
                    test_query = f"SELECT * FROM information_schema.tables WHERE table_name = '{payload}'"
                    cursor.execute(test_query)

                    # 如果查询成功执行，可能存在注入漏洞
                    vulnerabilities.append({
                        'type': 'sql_injection',
                        'severity': 'high',
                        'description': f'Potential SQL injection vulnerability detected with payload: {payload}',
                        'recommendation': 'Use parameterized queries and input validation'
                    })

                except Exception:
                    # 查询失败是正常的，说明有适当的错误处理
                    pass

            cursor.close()
            conn.close()

        except Exception as e:
            logger.debug(f"MySQL注入测试失败：{e}")

        return vulnerabilities

    def _test_postgresql_injection(self, target: str, db_info: Dict[str, Any], cred: Dict[str, str]) -> List[Dict[str, Any]]:
        """测试PostgreSQL SQL注入"""
        vulnerabilities = []

        try:
            conn = psycopg2.connect(
                host=target,
                port=db_info.get('port', 5432),
                user=cred['username'],
                password=cred['password'],
                database='postgres',
                connect_timeout=10
            )

            cursor = conn.cursor()

            # 测试基本SQL注入
            for payload in self.sql_injection_payloads:
                try:
                    # 构造测试查询
                    test_query = f"SELECT * FROM information_schema.tables WHERE table_name = '{payload}'"
                    cursor.execute(test_query)

                    vulnerabilities.append({
                        'type': 'sql_injection',
                        'severity': 'high',
                        'description': f'Potential SQL injection vulnerability detected with payload: {payload}',
                        'recommendation': 'Use parameterized queries and input validation'
                    })

                except Exception:
                    pass

            cursor.close()
            conn.close()

        except Exception as e:
            logger.debug(f"PostgreSQL注入测试失败：{e}")

        return vulnerabilities


# 使用示例
if __name__ == "__main__":
    scanner = DatabaseScanner()
    vuln_scanner = DatabaseVulnerabilityScanner()

    # 扫描单个目标的所有数据库服务
    result = scanner.scan_database_services("*************")
    print("数据库扫描结果：")
    print(json.dumps(result, indent=2, ensure_ascii=False))

    # 扫描特定端口
    mysql_result = scanner.scan_database_services("*************", [3306])
    print("\nMySQL扫描结果：")
    print(json.dumps(mysql_result, indent=2, ensure_ascii=False))
