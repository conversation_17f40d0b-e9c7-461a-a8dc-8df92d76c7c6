#收集全球存活的域名，
#为后面的数据分析提供数据
#可以被用来爬虫
#可以被用来web扫描


#从ip_segment_task表中获取待扫描的ip_segment
#将网段拆分成单个ip，然后判断当前主机的系统是什么，最后对该ip进行dns反查
#最后将domain和ip存入数据库中
import pymysql
import ipaddress
import socket
import configparser

def get_db_connection():
    """从配置文件中获取数据库连接"""
    config = configparser.ConfigParser()
    config.read('..\\..\\config\\config.config', encoding='utf-8')
    db_config = {
        'host': config['mysql']['host'],
        'port': int(config['mysql']['port']),
        'user': config['mysql']['user'],
        'password': config['mysql']['password'],
        'database': config['mysql']['database']
    }
    return pymysql.connect(**db_config)

def get_ip_segments():
    """从ip_segment_task表中获取待扫描的ip_segment"""
    try:
        db = get_db_connection()  # 使用新的数据库连接方法
        cursor = db.cursor()
        query = "SELECT ip_segment FROM ip_segment_task WHERE domain_status = 'todo' limit 1;"
        cursor.execute(query)
        ip_segment = cursor.fetchone()
        cursor.close()
        db.close()
        print('网段已获取：',ip_segment[0])
        return ip_segment[0]
    except Exception as e:
        print(f"Error getting IP segments: {e}")
        return 

def reverse_dns_lookup(ip):
    """对IP进行DNS反查"""
    try:
        print('正在反查：',ip)
        domain = socket.gethostbyaddr(ip)
        return domain[0]
    except socket.herror:
        return None

def save_domain_ip(domain, ip):
    print('正在保存域名及ip', domain, ip)
    """将domain和ip存入数据库中"""
    db = None  # 初始化db变量
    try:
        db = get_db_connection()  # 使用新的数据库连接方法
        cursor = db.cursor()
        query = "INSERT INTO domain_ip_mapping (domain, ip_address) VALUES (%s, %s)"
        cursor.execute(query, (domain, ip))
        db.commit()
        cursor.close()
    except Exception as e:
        print(f"Error saving domain and IP: {e}")
        if db:
            db.rollback()
    finally:
        if db and db.open:  # 确保连接是打开的
            db.close()

def update_segment_status(ip_segment, status):
    print('正在更新状态:',ip_segment,status)
    """更新IP网段的处理状态"""
    try:
        db = get_db_connection()
        cursor = db.cursor()
        query = "UPDATE ip_segment_task SET domain_status = %s WHERE ip_segment = %s"
        cursor.execute(query, (status, ip_segment))
        db.commit()
        cursor.close()
        db.close()
    except Exception as e:
        print(f"更新状态时出错: {e}")
        if db:
            db.rollback()
            db.close()

def process_ip_segments():
    while True:
        ip_segment = get_ip_segments()
        if ip_segment:
            network = ipaddress.ip_network(ip_segment)
            
            for ip in network.hosts():
                ip_str = str(ip)
                print('开始扫描ip：', ip_str)
                domain = reverse_dns_lookup(ip_str)
                if domain:
                    save_domain_ip(domain, ip_str)
            
            # 将该网段的状态变更为完成
            update_segment_status(ip_segment, 'done')



# 执行处理
process_ip_segments()