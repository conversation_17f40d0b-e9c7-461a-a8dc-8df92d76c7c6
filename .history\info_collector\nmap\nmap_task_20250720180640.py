#用于将ip分为不同的c段，然后插入扫描任务供扫描器扫描
#要求排除局域网ip
#要求支持IPv6
#任务切割完成后插入到数据库中


import os
import ipaddress
import pymysql
import configparser
import time
import traceback
from typing import Optional

def insert_scan_task(ip_range):
    print('start insert:', ip_range)
    """插入扫描任务到数据库"""
    # 使用os.path构建绝对路径
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
    config = configparser.ConfigParser()
    config.read(config_path, encoding='utf-8')  # 指定编码为utf-8
    if not config.has_section('mysql'):
        raise ValueError("MySQL section not found in config file")
    
    # 获取数据库配置
    db_config = {
        'host': config['mysql']['host'],
        'port': int(config['mysql']['port']),
        'user': config['mysql']['user'],
        'password': config['mysql']['password'],
        'database': config['mysql']['database'],
        'connect_timeout': 60,  # 增加连接超时设置
        'read_timeout': 60,     # 增加读取超时设置
        'write_timeout': 60     # 增加写入超时设置
    }
    
    db = pymysql.connect(**db_config)  # 使用pymysql.connect而不是pymysql.connector.connect
    cursor = db.cursor()

    try:
        db.ping(reconnect=True)
        print('sql execute')
        network = ipaddress.ip_network(ip_range)
        # 判断整个网段是否是局域网网段
        if not network.is_private:
            query = """
            INSERT INTO ip_segment_task (ip_segment, scan_type, status)
            VALUES (%s, %s, %s)
            ON DUPLICATE KEY UPDATE status = VALUES(status)
            """
            print(query)
            cursor.execute(query, (str(network), 'nmap', 'todo'))
        else:
            print('private ip segment')
        db.commit()

    except ValueError as e:
        print(f"Invalid IP range: {e}")
    finally:
        cursor.close()
        db.close()

def get_last_progress(task_type):
    """从数据库中获取上次扫描的进度"""
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
    config = configparser.ConfigParser()
    config.read(config_path, encoding='utf-8')
    
    db_config = {
        'host': config['mysql']['host'],
        'port': int(config['mysql']['port']),
        'user': config['mysql']['user'],
        'password': config['mysql']['password'],
        'database': config['mysql']['database']
    }
    
    db = pymysql.connect(**db_config)
    cursor = db.cursor()
    

    query = "SELECT task_tmp FROM task_tmp WHERE task_type = %s"
    print(query)

    cursor.execute(query, (task_type,))
    result = cursor.fetchone()
    print(result)
    cursor.close()
    db.close()
    
    return result[0] if result else None

def update_progress(task_type, ip_range):
    """更新数据库中的扫描进度"""
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
    config = configparser.ConfigParser()
    config.read(config_path, encoding='utf-8')
    
    db_config = {
        'host': config['mysql']['host'],
        'port': int(config['mysql']['port']),
        'user': config['mysql']['user'],
        'password': config['mysql']['password'],
        'database': config['mysql']['database']
    }
    
    db = pymysql.connect(**db_config)
    cursor = db.cursor()
    

    query = "INSERT INTO task_tmp (task_type, task_tmp) VALUES (%s, %s) ON DUPLICATE KEY UPDATE task_tmp = %s"
    cursor.execute(query, (task_type, ip_range, ip_range))
    db.commit()
    cursor.close()
    db.close()

def generate_ip_ranges():
    """生成所有A段、B段和C段的IP范围"""
    last_ip_range = get_last_progress('ipv4')
    print('last_ip_range', last_ip_range)
    skip = True if last_ip_range else False
    for i in range(0, 256):
        for j in range(0, 256):
            for k in range(0, 256):
                ip_range = f"{i}.{j}.{k}.0/24"
                # 判断是否为局域网网段
                try:
                    network = ipaddress.ip_network(ip_range)
                    if network.is_private:
                        # print('private network', ip_range)
                        continue  # 跳过局域网网段
                except ValueError as e:
                    print(f"local IP range: {e}")
                    continue
                
                if skip and ip_range <= last_ip_range:
                    # print('scanned', ip_range)
                    continue  # 跳过已扫描的部分
                skip = False
                print('ip_range', ip_range)
                insert_scan_task(ip_range)
                update_progress('ipv4', ip_range)  # 更新进度

def generate_ipv6_ranges():
    """生成常见的IPv6范围"""
    last_ip_range = get_last_progress('ipv6')
    print('last_ip_range', last_ip_range)
    skip = True if last_ip_range else False
    global_unicast = ipaddress.ip_network('2000::/3')
    for subnet in global_unicast.subnets(new_prefix=64):
        ipv6_range = str(subnet)
        # 判断是否为局域网网段
        try:
            network = ipaddress.ip_network(ipv6_range)
            if network.is_private:
                # print('private network', ipv6_range)
                continue  # 跳过局域网网段
        except ValueError as e:
            print(f"local IP range: {e}")
            continue
            
        if skip and ipv6_range <= last_ip_range:
            # print('scanned', ipv6_range)
            continue  # 跳过已扫描的部分
        skip = False
        print('ipv6_range', ipv6_range)
        insert_scan_task(ipv6_range)
        update_progress('ipv6', ipv6_range)  # 更新进度


# 使用示例
def main():
    generate_ip_ranges()
    generate_ipv6_ranges()
# if __name__ == "__main__":
#     # 生成所有A段、B段和C段的IP范围
#     generate_ip_ranges()
    
#     # 生成IPv6范围
#     generate_ipv6_ranges()
main()