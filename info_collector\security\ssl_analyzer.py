"""
SSL/TLS深度分析器

缺失功能：
1. SSL/TLS配置分析 - 协议版本、加密套件、证书链
2. 证书详细信息 - 有效期、签名算法、扩展字段
3. SSL漏洞检测 - Heartbleed、POODLE、BEAST等
4. 加密强度评估 - 密钥长度、算法安全性
5. HSTS配置检查
6. 证书透明度日志查询
7. SSL配置评分

作者：AI渗透测试系统
版本：1.0.0
"""

import ssl
import socket
import datetime
import hashlib
import base64
import json
import requests
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urlparse
import logging
import subprocess
import re

logger = logging.getLogger(__name__)


class SSLCertificateAnalyzer:
    """SSL证书分析器"""
    
    def __init__(self):
        self.ct_logs = [
            "https://ct.googleapis.com/logs/argon2024/",
            "https://ct.googleapis.com/logs/xenon2024/",
            "https://ct.cloudflare.com/logs/nimbus2024/"
        ]
    
    def analyze_certificate(self, hostname: str, port: int = 443) -> Dict[str, Any]:
        """
        分析SSL证书
        
        Args:
            hostname: 主机名
            port: 端口号
            
        Returns:
            Dict: 证书分析结果
        """
        logger.info(f"开始分析SSL证书：{hostname}:{port}")
        
        cert_info = {
            'hostname': hostname,
            'port': port,
            'scan_time': datetime.datetime.now().isoformat(),
            'certificate': {},
            'chain': [],
            'validation': {},
            'vulnerabilities': [],
            'transparency_logs': []
        }
        
        try:
            # 获取证书
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    # 获取对等证书
                    peer_cert = ssock.getpeercert(binary_form=True)
                    peer_cert_decoded = ssock.getpeercert()
                    
                    # 获取证书链
                    cert_chain = ssock.getpeercert_chain()
                    
                    # 分析主证书
                    cert_info['certificate'] = self._analyze_single_certificate(peer_cert_decoded, peer_cert)
                    
                    # 分析证书链
                    if cert_chain:
                        for i, cert in enumerate(cert_chain):
                            chain_cert_info = self._analyze_chain_certificate(cert, i)
                            cert_info['chain'].append(chain_cert_info)
                    
                    # 证书验证
                    cert_info['validation'] = self._validate_certificate(hostname, peer_cert_decoded)
                    
                    # 漏洞检测
                    cert_info['vulnerabilities'] = self._detect_certificate_vulnerabilities(peer_cert_decoded)
                    
                    # 证书透明度日志查询
                    cert_info['transparency_logs'] = self._query_certificate_transparency(hostname)
        
        except Exception as e:
            logger.error(f"SSL证书分析失败：{e}")
            cert_info['error'] = str(e)
        
        return cert_info
    
    def _analyze_single_certificate(self, cert_decoded: Dict, cert_binary: bytes) -> Dict[str, Any]:
        """分析单个证书"""
        cert_info = {
            'subject': cert_decoded.get('subject', []),
            'issuer': cert_decoded.get('issuer', []),
            'version': cert_decoded.get('version', 0),
            'serial_number': cert_decoded.get('serialNumber', ''),
            'not_before': cert_decoded.get('notBefore', ''),
            'not_after': cert_decoded.get('notAfter', ''),
            'signature_algorithm': cert_decoded.get('signatureAlgorithm', ''),
            'public_key': {},
            'extensions': [],
            'fingerprints': {},
            'key_usage': [],
            'san': []
        }
        
        # 解析主题和颁发者
        cert_info['subject_cn'] = self._extract_cn(cert_decoded.get('subject', []))
        cert_info['issuer_cn'] = self._extract_cn(cert_decoded.get('issuer', []))
        
        # 解析扩展字段
        if 'subjectAltName' in cert_decoded:
            cert_info['san'] = [name[1] for name in cert_decoded['subjectAltName']]
        
        # 计算指纹
        cert_info['fingerprints'] = {
            'sha1': hashlib.sha1(cert_binary).hexdigest(),
            'sha256': hashlib.sha256(cert_binary).hexdigest(),
            'md5': hashlib.md5(cert_binary).hexdigest()
        }
        
        # 解析有效期
        try:
            not_before = datetime.datetime.strptime(cert_decoded['notBefore'], '%b %d %H:%M:%S %Y %Z')
            not_after = datetime.datetime.strptime(cert_decoded['notAfter'], '%b %d %H:%M:%S %Y %Z')
            now = datetime.datetime.now()
            
            cert_info['validity'] = {
                'not_before': not_before.isoformat(),
                'not_after': not_after.isoformat(),
                'days_until_expiry': (not_after - now).days,
                'is_expired': now > not_after,
                'is_not_yet_valid': now < not_before
            }
        except Exception as e:
            logger.debug(f"解析证书有效期失败：{e}")
        
        return cert_info
    
    def _analyze_chain_certificate(self, cert, index: int) -> Dict[str, Any]:
        """分析证书链中的证书"""
        try:
            # 这里需要使用cryptography库来解析DER格式的证书
            # 简化实现，返回基本信息
            return {
                'index': index,
                'type': 'intermediate' if index > 0 else 'leaf',
                'der_size': len(cert) if cert else 0
            }
        except Exception as e:
            logger.debug(f"分析证书链失败：{e}")
            return {'index': index, 'error': str(e)}
    
    def _extract_cn(self, subject_or_issuer: List) -> Optional[str]:
        """提取CN字段"""
        for item in subject_or_issuer:
            for field in item:
                if field[0] == 'commonName':
                    return field[1]
        return None
    
    def _validate_certificate(self, hostname: str, cert: Dict) -> Dict[str, Any]:
        """验证证书"""
        validation = {
            'hostname_match': False,
            'self_signed': False,
            'expired': False,
            'weak_signature': False,
            'issues': []
        }
        
        try:
            # 检查主机名匹配
            subject_cn = self._extract_cn(cert.get('subject', []))
            san_names = [name[1] for name in cert.get('subjectAltName', [])]
            
            if subject_cn == hostname or hostname in san_names:
                validation['hostname_match'] = True
            else:
                validation['issues'].append('Hostname mismatch')
            
            # 检查是否自签名
            subject_cn = self._extract_cn(cert.get('subject', []))
            issuer_cn = self._extract_cn(cert.get('issuer', []))
            if subject_cn == issuer_cn:
                validation['self_signed'] = True
                validation['issues'].append('Self-signed certificate')
            
            # 检查是否过期
            try:
                not_after = datetime.datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                if datetime.datetime.now() > not_after:
                    validation['expired'] = True
                    validation['issues'].append('Certificate expired')
            except:
                pass
            
            # 检查签名算法强度
            sig_alg = cert.get('signatureAlgorithm', '').lower()
            if 'md5' in sig_alg or 'sha1' in sig_alg:
                validation['weak_signature'] = True
                validation['issues'].append(f'Weak signature algorithm: {sig_alg}')
        
        except Exception as e:
            logger.debug(f"证书验证失败：{e}")
            validation['issues'].append(f'Validation error: {str(e)}')
        
        return validation
    
    def _detect_certificate_vulnerabilities(self, cert: Dict) -> List[Dict[str, Any]]:
        """检测证书相关漏洞"""
        vulnerabilities = []
        
        try:
            # 检查弱密钥长度
            # 注意：这里需要更详细的公钥信息，简化实现
            
            # 检查弱签名算法
            sig_alg = cert.get('signatureAlgorithm', '').lower()
            if 'md5' in sig_alg:
                vulnerabilities.append({
                    'type': 'weak_signature_algorithm',
                    'severity': 'high',
                    'description': 'MD5 signature algorithm is cryptographically broken',
                    'algorithm': sig_alg
                })
            elif 'sha1' in sig_alg:
                vulnerabilities.append({
                    'type': 'weak_signature_algorithm',
                    'severity': 'medium',
                    'description': 'SHA1 signature algorithm is deprecated',
                    'algorithm': sig_alg
                })
            
            # 检查证书有效期过长
            try:
                not_before = datetime.datetime.strptime(cert['notBefore'], '%b %d %H:%M:%S %Y %Z')
                not_after = datetime.datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                validity_days = (not_after - not_before).days
                
                if validity_days > 825:  # CA/Browser Forum规定
                    vulnerabilities.append({
                        'type': 'excessive_validity_period',
                        'severity': 'low',
                        'description': f'Certificate validity period ({validity_days} days) exceeds recommended maximum',
                        'validity_days': validity_days
                    })
            except:
                pass
        
        except Exception as e:
            logger.debug(f"证书漏洞检测失败：{e}")
        
        return vulnerabilities
    
    def _query_certificate_transparency(self, hostname: str) -> List[Dict[str, Any]]:
        """查询证书透明度日志"""
        ct_results = []
        
        try:
            # 查询crt.sh数据库
            url = f"https://crt.sh/?q={hostname}&output=json"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                ct_data = response.json()
                for entry in ct_data[:10]:  # 限制返回数量
                    ct_results.append({
                        'id': entry.get('id'),
                        'logged_at': entry.get('entry_timestamp'),
                        'not_before': entry.get('not_before'),
                        'not_after': entry.get('not_after'),
                        'common_name': entry.get('common_name'),
                        'issuer_name': entry.get('issuer_name')
                    })
        
        except Exception as e:
            logger.debug(f"证书透明度查询失败：{e}")
        
        return ct_results


class SSLConfigurationAnalyzer:
    """SSL配置分析器"""
    
    def __init__(self):
        self.weak_ciphers = [
            'RC4', 'DES', '3DES', 'MD5', 'NULL', 'EXPORT', 'anon'
        ]
        
        self.deprecated_protocols = [
            'SSLv2', 'SSLv3', 'TLSv1.0', 'TLSv1.1'
        ]
    
    def analyze_ssl_configuration(self, hostname: str, port: int = 443) -> Dict[str, Any]:
        """
        分析SSL配置
        
        Args:
            hostname: 主机名
            port: 端口号
            
        Returns:
            Dict: SSL配置分析结果
        """
        logger.info(f"开始分析SSL配置：{hostname}:{port}")
        
        ssl_config = {
            'hostname': hostname,
            'port': port,
            'scan_time': datetime.datetime.now().isoformat(),
            'supported_protocols': [],
            'cipher_suites': [],
            'vulnerabilities': [],
            'security_headers': {},
            'configuration_score': 0,
            'recommendations': []
        }
        
        try:
            # 检测支持的协议
            ssl_config['supported_protocols'] = self._detect_supported_protocols(hostname, port)
            
            # 检测支持的密码套件
            ssl_config['cipher_suites'] = self._detect_cipher_suites(hostname, port)
            
            # SSL漏洞检测
            ssl_config['vulnerabilities'] = self._detect_ssl_vulnerabilities(hostname, port)
            
            # 安全头检测
            ssl_config['security_headers'] = self._check_security_headers(hostname, port)
            
            # 配置评分
            ssl_config['configuration_score'] = self._calculate_ssl_score(ssl_config)
            
            # 生成建议
            ssl_config['recommendations'] = self._generate_recommendations(ssl_config)
        
        except Exception as e:
            logger.error(f"SSL配置分析失败：{e}")
            ssl_config['error'] = str(e)
        
        return ssl_config
    
    def _detect_supported_protocols(self, hostname: str, port: int) -> List[Dict[str, Any]]:
        """检测支持的SSL/TLS协议"""
        protocols = []
        
        # 测试不同的SSL/TLS版本
        test_protocols = [
            ('SSLv2', ssl.PROTOCOL_SSLv23),
            ('SSLv3', ssl.PROTOCOL_SSLv23),
            ('TLSv1.0', ssl.PROTOCOL_TLSv1),
            ('TLSv1.1', ssl.PROTOCOL_TLSv1_1),
            ('TLSv1.2', ssl.PROTOCOL_TLSv1_2),
        ]
        
        # 添加TLS 1.3支持检测
        if hasattr(ssl, 'PROTOCOL_TLSv1_3'):
            test_protocols.append(('TLSv1.3', ssl.PROTOCOL_TLSv1_3))
        
        for protocol_name, protocol_const in test_protocols:
            try:
                context = ssl.SSLContext(protocol_const)
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                
                with socket.create_connection((hostname, port), timeout=5) as sock:
                    with context.wrap_socket(sock) as ssock:
                        protocols.append({
                            'protocol': protocol_name,
                            'supported': True,
                            'deprecated': protocol_name in self.deprecated_protocols
                        })
            except Exception as e:
                protocols.append({
                    'protocol': protocol_name,
                    'supported': False,
                    'error': str(e)
                })
        
        return protocols
    
    def _detect_cipher_suites(self, hostname: str, port: int) -> List[Dict[str, Any]]:
        """检测支持的密码套件"""
        cipher_suites = []
        
        try:
            # 使用默认上下文获取密码套件
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cipher = ssock.cipher()
                    if cipher:
                        cipher_suites.append({
                            'name': cipher[0],
                            'protocol': cipher[1],
                            'key_size': cipher[2],
                            'is_weak': any(weak in cipher[0] for weak in self.weak_ciphers)
                        })
        
        except Exception as e:
            logger.debug(f"密码套件检测失败：{e}")
        
        return cipher_suites
    
    def _detect_ssl_vulnerabilities(self, hostname: str, port: int) -> List[Dict[str, Any]]:
        """检测SSL漏洞"""
        vulnerabilities = []
        
        # 检测Heartbleed
        if self._test_heartbleed(hostname, port):
            vulnerabilities.append({
                'name': 'Heartbleed',
                'cve': 'CVE-2014-0160',
                'severity': 'critical',
                'description': 'OpenSSL Heartbleed vulnerability allows memory disclosure'
            })
        
        # 检测POODLE
        if self._test_poodle(hostname, port):
            vulnerabilities.append({
                'name': 'POODLE',
                'cve': 'CVE-2014-3566',
                'severity': 'medium',
                'description': 'SSLv3 POODLE vulnerability'
            })
        
        # 检测弱密码套件
        weak_ciphers = self._check_weak_ciphers(hostname, port)
        if weak_ciphers:
            vulnerabilities.append({
                'name': 'Weak Cipher Suites',
                'severity': 'medium',
                'description': 'Server supports weak cipher suites',
                'weak_ciphers': weak_ciphers
            })
        
        return vulnerabilities
    
    def _test_heartbleed(self, hostname: str, port: int) -> bool:
        """测试Heartbleed漏洞"""
        try:
            # 简化的Heartbleed检测
            # 实际实现需要发送特制的heartbeat请求
            return False
        except:
            return False
    
    def _test_poodle(self, hostname: str, port: int) -> bool:
        """测试POODLE漏洞"""
        try:
            # 检测是否支持SSLv3
            context = ssl.SSLContext(ssl.PROTOCOL_SSLv23)
            context.options |= ssl.OP_NO_TLSv1 | ssl.OP_NO_TLSv1_1 | ssl.OP_NO_TLSv1_2
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            with socket.create_connection((hostname, port), timeout=5) as sock:
                with context.wrap_socket(sock) as ssock:
                    return True  # 如果连接成功，说明支持SSLv3
        except:
            return False
    
    def _check_weak_ciphers(self, hostname: str, port: int) -> List[str]:
        """检查弱密码套件"""
        weak_ciphers_found = []
        
        try:
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cipher = ssock.cipher()
                    if cipher:
                        cipher_name = cipher[0]
                        for weak_cipher in self.weak_ciphers:
                            if weak_cipher.upper() in cipher_name.upper():
                                weak_ciphers_found.append(cipher_name)
        
        except Exception as e:
            logger.debug(f"弱密码套件检查失败：{e}")
        
        return weak_ciphers_found
    
    def _check_security_headers(self, hostname: str, port: int) -> Dict[str, Any]:
        """检查安全头"""
        security_headers = {
            'hsts': {'present': False, 'max_age': None, 'include_subdomains': False},
            'hpkp': {'present': False},
            'expect_ct': {'present': False}
        }
        
        try:
            # 发送HTTP请求检查安全头
            url = f"https://{hostname}:{port}" if port != 443 else f"https://{hostname}"
            response = requests.head(url, timeout=10, verify=False)
            
            # 检查HSTS
            hsts_header = response.headers.get('Strict-Transport-Security')
            if hsts_header:
                security_headers['hsts']['present'] = True
                if 'max-age=' in hsts_header:
                    max_age_match = re.search(r'max-age=(\d+)', hsts_header)
                    if max_age_match:
                        security_headers['hsts']['max_age'] = int(max_age_match.group(1))
                security_headers['hsts']['include_subdomains'] = 'includeSubDomains' in hsts_header
            
            # 检查HPKP
            if response.headers.get('Public-Key-Pins'):
                security_headers['hpkp']['present'] = True
            
            # 检查Expect-CT
            if response.headers.get('Expect-CT'):
                security_headers['expect_ct']['present'] = True
        
        except Exception as e:
            logger.debug(f"安全头检查失败：{e}")
        
        return security_headers
    
    def _calculate_ssl_score(self, ssl_config: Dict[str, Any]) -> int:
        """计算SSL配置评分"""
        score = 100
        
        # 协议评分
        for protocol in ssl_config.get('supported_protocols', []):
            if protocol['supported'] and protocol.get('deprecated', False):
                score -= 20
        
        # 漏洞评分
        for vuln in ssl_config.get('vulnerabilities', []):
            if vuln['severity'] == 'critical':
                score -= 40
            elif vuln['severity'] == 'high':
                score -= 30
            elif vuln['severity'] == 'medium':
                score -= 20
            elif vuln['severity'] == 'low':
                score -= 10
        
        # 安全头评分
        headers = ssl_config.get('security_headers', {})
        if not headers.get('hsts', {}).get('present', False):
            score -= 10
        
        return max(0, score)
    
    def _generate_recommendations(self, ssl_config: Dict[str, Any]) -> List[str]:
        """生成安全建议"""
        recommendations = []
        
        # 协议建议
        for protocol in ssl_config.get('supported_protocols', []):
            if protocol['supported'] and protocol.get('deprecated', False):
                recommendations.append(f"禁用已弃用的协议：{protocol['protocol']}")
        
        # 漏洞建议
        for vuln in ssl_config.get('vulnerabilities', []):
            recommendations.append(f"修复{vuln['severity']}级别漏洞：{vuln['name']}")
        
        # 安全头建议
        headers = ssl_config.get('security_headers', {})
        if not headers.get('hsts', {}).get('present', False):
            recommendations.append("启用HSTS (HTTP Strict Transport Security)")
        
        return recommendations


# 使用示例
if __name__ == "__main__":
    cert_analyzer = SSLCertificateAnalyzer()
    config_analyzer = SSLConfigurationAnalyzer()
    
    # 测试证书分析
    cert_result = cert_analyzer.analyze_certificate("www.google.com")
    print("证书分析结果：")
    print(json.dumps(cert_result, indent=2, ensure_ascii=False))
    
    # 测试SSL配置分析
    config_result = config_analyzer.analyze_ssl_configuration("www.google.com")
    print("\nSSL配置分析结果：")
    print(json.dumps(config_result, indent=2, ensure_ascii=False))
