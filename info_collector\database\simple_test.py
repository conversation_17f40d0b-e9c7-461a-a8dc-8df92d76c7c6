"""
数据库扫描器简化测试脚本

功能：
1. 测试基础端口扫描
2. 测试数据库类型识别
3. 测试漏洞检测逻辑
4. 不依赖实际数据库连接

作者：AI渗透测试系统
版本：1.0.0
"""

import os
import sys
import json
import time

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from info_collector.database.database_scanner import DatabaseScanner
from info_collector.database.database_config import get_default_config, DATABASE_PORTS


def test_port_scanning():
    """测试端口扫描功能"""
    print("=" * 50)
    print("测试端口扫描功能")
    print("=" * 50)
    
    scanner = DatabaseScanner()
    
    # 测试本地常见端口
    test_ports = [80, 443, 3306, 5432, 27017, 6379, 9200]
    target = "127.0.0.1"
    
    print(f"扫描目标：{target}")
    print(f"测试端口：{test_ports}")
    
    open_ports = []
    for port in test_ports:
        try:
            is_open = scanner._check_port_open(target, port, timeout=2)
            status = "开放" if is_open else "关闭"
            print(f"  端口 {port}: {status}")
            if is_open:
                open_ports.append(port)
        except Exception as e:
            print(f"  端口 {port}: 检查失败 - {e}")
    
    print(f"\n发现开放端口：{open_ports}")
    return open_ports


def test_database_identification():
    """测试数据库类型识别"""
    print("\n" + "=" * 50)
    print("测试数据库类型识别")
    print("=" * 50)
    
    scanner = DatabaseScanner()
    
    # 测试数据库端口映射
    print("数据库端口映射：")
    for db_type, ports in DATABASE_PORTS.items():
        print(f"  {db_type}: {ports}")
    
    # 测试指纹识别逻辑
    print("\n测试指纹特征：")
    for db_type, fingerprints in scanner.database_fingerprints.items():
        print(f"  {db_type}:")
        print(f"    Banner特征: {len(fingerprints.get('banners', []))}个")
        print(f"    错误消息: {fingerprints.get('error_messages', [])}")


def test_vulnerability_detection():
    """测试漏洞检测逻辑"""
    print("\n" + "=" * 50)
    print("测试漏洞检测逻辑")
    print("=" * 50)
    
    scanner = DatabaseScanner()
    
    # 模拟不同类型的数据库信息
    test_databases = [
        {
            'type': 'MySQL',
            'host': '127.0.0.1',
            'port': 3306,
            'version': '5.7.10',
            'authentication': {
                'anonymous_access': True,
                'weak_credentials': [{'username': 'root', 'password': ''}]
            },
            'configuration': {
                'ssl_enabled': False,
                'general_log': 'OFF'
            }
        },
        {
            'type': 'MongoDB',
            'host': '127.0.0.1',
            'port': 27017,
            'version': '3.6.5',
            'authentication': {
                'no_auth_required': True
            }
        },
        {
            'type': 'Redis',
            'host': '127.0.0.1',
            'port': 6379,
            'version': '4.0.2',
            'authentication': {
                'no_auth_required': True
            }
        }
    ]
    
    for db_info in test_databases:
        print(f"\n测试 {db_info['type']} 漏洞检测：")
        try:
            vulnerabilities = scanner._detect_database_vulnerabilities('127.0.0.1', db_info)
            print(f"  发现漏洞：{len(vulnerabilities)}个")
            
            for vuln in vulnerabilities:
                severity = vuln.get('severity', 'unknown').upper()
                description = vuln.get('description', 'Unknown')
                print(f"    [{severity}] {description}")
                
                if vuln.get('cve'):
                    print(f"      CVE: {vuln['cve']}")
                if vuln.get('recommendation'):
                    print(f"      建议: {vuln['recommendation']}")
        
        except Exception as e:
            print(f"  漏洞检测失败：{e}")


def test_configuration_checks():
    """测试配置检查"""
    print("\n" + "=" * 50)
    print("测试配置检查")
    print("=" * 50)
    
    scanner = DatabaseScanner()
    
    # 测试不同配置场景
    test_configs = [
        {
            'name': 'MySQL - 安全配置',
            'db_info': {
                'type': 'MySQL',
                'configuration': {
                    'ssl_enabled': True,
                    'general_log': 'ON'
                },
                'authentication': {
                    'weak_credentials': []
                }
            }
        },
        {
            'name': 'MySQL - 不安全配置',
            'db_info': {
                'type': 'MySQL',
                'configuration': {
                    'ssl_enabled': False,
                    'general_log': 'OFF'
                },
                'authentication': {
                    'weak_credentials': [{'username': 'root', 'password': '123456'}]
                }
            }
        },
        {
            'name': 'MongoDB - 无认证',
            'db_info': {
                'type': 'MongoDB',
                'authentication': {
                    'no_auth_required': True
                }
            }
        }
    ]
    
    for test_config in test_configs:
        print(f"\n{test_config['name']}:")
        try:
            config_vulns = scanner._check_configuration_vulnerabilities(test_config['db_info'])
            auth_vulns = scanner._check_authentication_vulnerabilities(test_config['db_info'])
            
            all_vulns = config_vulns + auth_vulns
            print(f"  配置问题：{len(all_vulns)}个")
            
            for vuln in all_vulns:
                severity = vuln.get('severity', 'unknown').upper()
                description = vuln.get('description', 'Unknown')
                print(f"    [{severity}] {description}")
        
        except Exception as e:
            print(f"  配置检查失败：{e}")


def test_scan_integration():
    """测试扫描集成"""
    print("\n" + "=" * 50)
    print("测试扫描集成")
    print("=" * 50)
    
    scanner = DatabaseScanner()
    
    # 测试完整扫描流程（不连接实际数据库）
    target = "127.0.0.1"
    test_ports = [3306, 5432, 27017]  # 只测试几个端口
    
    print(f"执行完整扫描：{target}")
    print(f"扫描端口：{test_ports}")
    
    try:
        start_time = time.time()
        results = scanner.scan_database_services(target, test_ports)
        scan_time = time.time() - start_time
        
        print(f"\n扫描完成，耗时：{scan_time:.2f}秒")
        print(f"发现数据库：{len(results.get('discovered_databases', []))}个")
        print(f"发现漏洞：{len(results.get('vulnerabilities', []))}个")
        print(f"安全问题：{len(results.get('security_issues', []))}个")
        print(f"安全建议：{len(results.get('recommendations', []))}个")
        
        # 显示详细结果
        if results.get('discovered_databases'):
            print("\n发现的数据库：")
            for db in results['discovered_databases']:
                print(f"  - {db.get('type', 'Unknown')} on {db.get('host')}:{db.get('port')}")
                if db.get('version'):
                    print(f"    版本：{db['version']}")
        
        if results.get('vulnerabilities'):
            print("\n发现的漏洞：")
            for vuln in results['vulnerabilities'][:5]:  # 只显示前5个
                severity = vuln.get('severity', 'unknown').upper()
                description = vuln.get('description', 'Unknown')
                print(f"  - [{severity}] {description}")
        
        if results.get('recommendations'):
            print("\n安全建议：")
            for rec in results['recommendations'][:5]:  # 只显示前5个
                print(f"  - {rec}")
    
    except Exception as e:
        print(f"扫描失败：{e}")


def test_performance():
    """测试性能"""
    print("\n" + "=" * 50)
    print("性能测试")
    print("=" * 50)
    
    scanner = DatabaseScanner()
    
    # 测试端口检查性能
    target = "127.0.0.1"
    test_ports = list(range(3300, 3320))  # 20个端口
    
    print(f"测试{len(test_ports)}个端口的扫描性能...")
    
    start_time = time.time()
    open_ports = []
    
    for port in test_ports:
        try:
            if scanner._check_port_open(target, port, timeout=1):
                open_ports.append(port)
        except:
            pass
    
    scan_time = time.time() - start_time
    
    print(f"扫描耗时：{scan_time:.2f}秒")
    print(f"平均每端口：{scan_time/len(test_ports)*1000:.2f}毫秒")
    print(f"开放端口：{open_ports}")


def main():
    """主函数"""
    print("数据库扫描器功能测试")
    print("测试时间：", time.strftime('%Y-%m-%d %H:%M:%S'))
    
    # 执行所有测试
    test_functions = [
        test_port_scanning,
        test_database_identification,
        test_vulnerability_detection,
        test_configuration_checks,
        test_scan_integration,
        test_performance
    ]
    
    for i, test_func in enumerate(test_functions, 1):
        try:
            print(f"\n[{i}/{len(test_functions)}] 执行：{test_func.__name__}")
            test_func()
        except Exception as e:
            print(f"测试失败：{e}")
    
    print("\n" + "=" * 50)
    print("所有测试完成")
    print("=" * 50)
    
    # 显示配置信息
    print("\n系统配置信息：")
    config = get_default_config()
    print(f"  - 最大并发扫描：{config.max_concurrent_scans}")
    print(f"  - 连接超时：{config.connection_timeout}秒")
    print(f"  - 启用的数据库：{len(config.enabled_databases)}个")
    print(f"  - 测试弱密码：{config.test_weak_passwords}")
    print(f"  - 检查漏洞：{config.check_version_vulnerabilities}")


if __name__ == "__main__":
    main()
