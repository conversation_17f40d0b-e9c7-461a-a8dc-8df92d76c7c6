"""
数据库初始化脚本

功能：
1. 连接数据库并创建必要的表结构
2. 执行SQL脚本初始化数据库
3. 验证表结构是否正确创建

作者：AI渗透测试系统
版本：1.0.0
"""

import os
import sys
import pymysql
import configparser
from typing import Optional

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))


def get_db_config(config_path: Optional[str] = None) -> dict:
    """获取数据库配置"""
    if config_path is None:
        config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
    
    config = configparser.ConfigParser()
    config.read(config_path, encoding='utf-8')
    
    return {
        'host': config['mysql']['host'],
        'port': int(config['mysql']['port']),
        'user': config['mysql']['user'],
        'password': config['mysql']['password'],
        'database': config['mysql']['database'],
        'charset': config.get('mysql', 'charset', fallback='utf8mb4'),
        'connect_timeout': 60,
        'read_timeout': 300,
        'write_timeout': 300
    }


def execute_sql_file(connection, sql_file_path: str) -> bool:
    """执行SQL文件"""
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句（简单的分割，基于分号）
        sql_statements = []
        current_statement = ""
        in_delimiter = False
        
        for line in sql_content.split('\n'):
            line = line.strip()
            
            # 跳过注释和空行
            if not line or line.startswith('--') or line.startswith('#'):
                continue
            
            # 处理DELIMITER语句
            if line.upper().startswith('DELIMITER'):
                in_delimiter = not in_delimiter
                continue
            
            current_statement += line + '\n'
            
            # 检查语句结束
            if not in_delimiter and line.endswith(';'):
                if current_statement.strip():
                    sql_statements.append(current_statement.strip())
                current_statement = ""
            elif in_delimiter and line.upper() == 'END //':
                current_statement += 'END'
                sql_statements.append(current_statement.strip())
                current_statement = ""
        
        # 添加最后一个语句（如果有）
        if current_statement.strip():
            sql_statements.append(current_statement.strip())
        
        # 执行SQL语句
        cursor = connection.cursor()
        success_count = 0
        error_count = 0
        
        for i, statement in enumerate(sql_statements):
            try:
                # 跳过空语句
                if not statement or statement.isspace():
                    continue
                
                print(f"执行SQL语句 {i+1}/{len(sql_statements)}: {statement[:50]}...")
                cursor.execute(statement)
                connection.commit()
                success_count += 1
                
            except Exception as e:
                error_count += 1
                print(f"执行SQL语句失败: {statement[:50]}...")
                print(f"错误: {e}")
                # 继续执行其他语句
                continue
        
        cursor.close()
        
        print(f"SQL文件执行完成: 成功 {success_count} 个, 失败 {error_count} 个")
        return error_count == 0
        
    except Exception as e:
        print(f"执行SQL文件失败: {e}")
        return False


def verify_tables(connection) -> bool:
    """验证表是否正确创建"""
    required_tables = [
        'target_assets',
        'network_info',
        'service_info',
        'web_app_info',
        'vulnerability_info',
        'threat_intelligence',
        'scan_tasks',
        'scan_results',
        'database_scan_results',
        'domain_ip_mapping'
    ]
    
    try:
        cursor = connection.cursor()
        cursor.execute("SHOW TABLES")
        existing_tables = [table[0] for table in cursor.fetchall()]
        cursor.close()
        
        print(f"数据库中现有表: {existing_tables}")
        
        missing_tables = []
        for table in required_tables:
            if table not in existing_tables:
                missing_tables.append(table)
        
        if missing_tables:
            print(f"缺少的表: {missing_tables}")
            return False
        else:
            print("✅ 所有必需的表都已创建")
            return True
            
    except Exception as e:
        print(f"验证表结构失败: {e}")
        return False


def check_table_structure(connection, table_name: str) -> dict:
    """检查表结构"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"DESCRIBE {table_name}")
        columns = cursor.fetchall()
        cursor.close()
        
        structure = {}
        for column in columns:
            structure[column[0]] = {
                'type': column[1],
                'null': column[2],
                'key': column[3],
                'default': column[4],
                'extra': column[5]
            }
        
        return structure
        
    except Exception as e:
        print(f"检查表结构失败 {table_name}: {e}")
        return {}


def init_database():
    """初始化数据库"""
    print("开始初始化数据库...")
    
    try:
        # 获取数据库配置
        db_config = get_db_config()
        print(f"连接数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        # 连接数据库
        connection = pymysql.connect(**db_config)
        print("✅ 数据库连接成功")
        
        # 执行SQL脚本
        sql_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'sql', 'database_storage_tables.sql')
        
        if not os.path.exists(sql_file_path):
            print(f"❌ SQL文件不存在: {sql_file_path}")
            return False
        
        print(f"执行SQL脚本: {sql_file_path}")
        success = execute_sql_file(connection, sql_file_path)
        
        if success:
            print("✅ SQL脚本执行成功")
        else:
            print("⚠️ SQL脚本执行部分失败，但继续验证...")
        
        # 验证表结构
        print("\n验证表结构...")
        tables_ok = verify_tables(connection)
        
        if tables_ok:
            print("✅ 数据库初始化完成")
            
            # 显示一些表的结构信息
            print("\n主要表结构信息:")
            for table in ['target_assets', 'vulnerability_info', 'service_info']:
                print(f"\n{table} 表结构:")
                structure = check_table_structure(connection, table)
                for col_name, col_info in structure.items():
                    print(f"  {col_name}: {col_info['type']}")
        else:
            print("❌ 表结构验证失败")
        
        connection.close()
        return tables_ok
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("数据库存储系统初始化")
    print("=" * 60)
    
    success = init_database()
    
    if success:
        print("\n🎉 数据库初始化成功！现在可以运行存储测试了。")
        
        # 提示如何运行测试
        print("\n运行以下命令测试存储功能:")
        print("python test_storage_integration.py")
        
    else:
        print("\n❌ 数据库初始化失败，请检查:")
        print("1. 数据库连接配置是否正确")
        print("2. 数据库用户是否有足够权限")
        print("3. SQL脚本文件是否存在")
    
    return success


if __name__ == "__main__":
    main()
