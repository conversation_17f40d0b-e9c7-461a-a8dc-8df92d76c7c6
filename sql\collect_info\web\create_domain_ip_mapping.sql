CREATE TABLE domain_ip_mapping (
    id INT AUTO_INCREMENT PRIMARY KEY,
    domain VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL ,
    status VARCHAR(45) NOT NULL DEFAULT 'todo',
    awvs_status VARCHAR(45) NOT NULL DEFAULT 'todo',
    awvs_scan_id VARCHAR(100) DEFAULT NULL,
    awvs_result json DEFAULT NULL,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_domain_ip (domain)
);