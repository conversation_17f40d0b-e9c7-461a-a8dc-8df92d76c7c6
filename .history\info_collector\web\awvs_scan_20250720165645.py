'''

对目标进行awvs扫描

'''

#从domain_ip_mapping表中获取待扫描的domain，条件是awvs_status为todo，取出第一条数据，取出domain

#然后对domain进行awvs扫描，扫描完成后，将awvs_status更新为done

#然后将扫描结果保存到awvs_scan_results表中

#awvs_scan_results表的字段为domain,scan_result

import requests
import pymysql
import configparser
import os
import time
import json
import urllib3
import traceback
from typing import Dict, Optional, Any, Union

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def get_db_connection() -> pymysql.connections.Connection:
    """
    从配置文件中获取数据库连接
    
    Returns:
        pymysql.connections.Connection: 数据库连接对象
    """
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
    config.read(config_path, encoding='utf-8')
    db_config = {
        'host': config['mysql']['host'],
        'port': int(config['mysql']['port']),
        'user': config['mysql']['user'],
        'password': config['mysql']['password'],
        'database': config['mysql']['database'],
        'connect_timeout': 60,
        'read_timeout': 300,
        'write_timeout': 300
    }
    return pymysql.connect(**db_config)


def get_todo_domain() -> Optional[str]:
    """
    从domain_ip_mapping表中获取待扫描的域名
    
    Returns:
        Optional[str]: 待扫描的域名，如果没有则返回None
    """
    try:
        db = get_db_connection()
        cursor = db.cursor()
        # 从domain_ip_mapping表中查询一条awvs_status = 'todo'的域名，并且这个域名没有awvs_status = 'done'的记录
        query = """
                SELECT d1.domain 
                FROM domain_ip_mapping d1
                WHERE d1.awvs_status = 'todo'
                AND NOT EXISTS (
                    SELECT 1 
                    FROM domain_ip_mapping d2 
                    WHERE d2.domain = d1.domain 
                    AND d2.awvs_status = 'done'
                )
                LIMIT 1
                """
        cursor.execute(query)
        domain = cursor.fetchone()
        cursor.close()
        db.close()
        if domain:
            print(f'获取到待扫描域名: {domain[0]}')
            return domain[0]
        return None
    except Exception as e:
        print(f"获取待扫描域名时出错: {e}")
        traceback.print_exc()
        return None


def update_domain_status(domain: str, status: str) -> bool:
    """
    更新域名的AWVS扫描状态
    
    Args:
        domain (str): 域名
        status (str): 状态值
        
    Returns:
        bool: 更新是否成功
    """
    try:
        db = get_db_connection()
        cursor = db.cursor()
        query = "UPDATE domain_ip_mapping SET awvs_status = %s WHERE domain = %s"
        cursor.execute(query, (status, domain))
        db.commit()
        cursor.close()
        db.close()
        return True
    except Exception as e:
        print(f"更新域名状态时出错: {e}")
        traceback.print_exc()
        return False


def save_scan_result(domain: str, scan_result: Union[Dict, Any]) -> bool:
    """
    保存AWVS扫描结果到数据库
    
    Args:
        domain (str): 域名
        scan_result (Union[Dict, Any]): 扫描结果
        
    Returns:
        bool: 保存是否成功
    """
    try:
        db = get_db_connection()
        cursor = db.cursor()

        # 确保scan_result是字典类型
        if not isinstance(scan_result, dict):
            scan_result = {"raw_data": str(scan_result)}

        # 将字典转换为合法的JSON字符串
        result_json = json.dumps(scan_result, ensure_ascii=False)
        print(f'扫描结果JSON长度: {len(result_json)}')
        
        # 使用参数化查询，让MySQL驱动正确处理JSON字符串
        query = '''
        UPDATE domain_ip_mapping 
        SET awvs_result = %s 
        WHERE domain = %s
        '''
        cursor.execute(query, (result_json, domain))
        
        # 检查是否有行被更新
        if cursor.rowcount == 0:
            # 如果没有更新任何行，则插入新记录
            insert_query = '''
            INSERT INTO domain_ip_mapping (domain, awvs_result, awvs_status) 
            VALUES (%s, %s, 'done')
            '''
            cursor.execute(insert_query, (domain, result_json))
            
        rows_affected = cursor.rowcount
        db.commit()
        cursor.close()
        db.close()
        print(f"成功保存扫描结果，影响行数: {cursor.rowcount}")
        print(f"成功保存{domain}扫描结果，结果是: {result_json}")
    except Exception as e:
        print(f"保存扫描结果时出错: {e}")
        traceback.print_exc()
        return False


def perform_awvs_scan(domain: str) -> Dict[str, Any]:
    """
    执行AWVS扫描
    
    Args:
        domain (str): 要扫描的域名
        
    Returns:
        Dict[str, Any]: 扫描结果
    """
    print(f"开始对 {domain} 进行AWVS扫描")
    
    # 从配置文件中读取AWVS API配置
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
    config.read(config_path, encoding='utf-8')
    
    # 获取AWVS配置
    awvs_api_url = config['awvs']['api_url']
    awvs_api_key = config['awvs']['api_key']
    awvs_profile_id = config['awvs']['profile_id']
    
    headers = {
        'X-Auth': awvs_api_key,
        'Content-Type': 'application/json'
    }
    
    # 创建目标
    target_data = {
        "address": domain,
        "description": "AWVS扫描目标",
        "criticality": 10
    }

    try:
        # 创建会话并禁用代理
        session = requests.Session()
        session.proxies = {}  # 清除所有代理设置
        session.verify = False  # 禁用SSL验证，因为AWVS通常使用自签名证书

        # 禁用SSL密钥日志记录以避免权限问题
        import ssl
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # 设置环境变量以禁用SSL密钥日志
        import os
        if 'SSLKEYLOGFILE' in os.environ:
            del os.environ['SSLKEYLOGFILE']

        # 使用session进行所有请求
        targets_url = f"{awvs_api_url}/api/v1/targets"
        response = session.post(targets_url, headers=headers, json=target_data, timeout=30)
        response.raise_for_status()
        target_id = response.json().get('target_id')

        if not target_id:
            print({"status": "error", "details": "无法获取目标ID"})
            return {"status": "error", "details": "无法获取目标ID"}

        # 启动扫描
        scan_data = {
            "target_id": target_id,
            "profile_id": awvs_profile_id,
            "schedule": {
                "disable": False,
                "start_date": None,
                "time_sensitive": False
            }
        }

        scans_url = f"{awvs_api_url}/api/v1/scans"
        response = session.post(scans_url, headers=headers, json=scan_data, timeout=30)
        response.raise_for_status()
        scan_id = response.json().get('scan_id')

        # 将扫描任务的id存入数据库
        try:
            db = get_db_connection()
            cursor = db.cursor()
            query = '''
            UPDATE domain_ip_mapping 
            SET awvs_scan_id = %s 
            WHERE domain = %s
            '''
            cursor.execute(query, (scan_id, domain))
            
            # 检查是否有行被更新
            if cursor.rowcount == 0:
                # 如果没有更新任何行，则插入新记录
                insert_query = '''
                INSERT INTO domain_ip_mapping (domain, awvs_scan_id, awvs_status) 
                VALUES (%s, %s, 'scanning')
                '''
                cursor.execute(insert_query, (domain, scan_id))
                
            db.commit()
            cursor.close()
            db.close()
            print(f"成功保存扫描ID: {scan_id}")
        except Exception as e:
            print(f"保存扫描ID时出错: {e}")
            # 打印更详细的错误信息
            import traceback
            traceback.print_exc()

        # 获取扫描结果需要等待扫描完成，这里简化处理
        print({"status": "scan_started", "target_id": target_id})
        # 轮询扫描状态
        scan_status_url = f"{awvs_api_url}/api/v1/scans/{scan_id}"
        while True:
            response = session.get(scan_status_url, headers=headers, timeout=30)
            response.raise_for_status()
            status = response.json().get('current_session', {}).get('status')

            if status == 'completed':
                break
            elif status in ['failed', 'aborted']:
                return {"status": "error", "details": f"扫描失败，状态: {status}"}

            print(f"扫描进行中，当前状态: {status}，等待30秒...")
            time.sleep(30)

        # 获取扫描结果
        results_url = f"{awvs_api_url}/api/v1/scans/{scan_id}/results"
        response = session.get(results_url, headers=headers, timeout=30)
        response.raise_for_status()

        return {
            "status": "completed",
            "target_id": target_id,
            "scan_id": scan_id,
            "results": response.json()
        }

    except requests.exceptions.RequestException as e:
        print(f"AWVS扫描请求失败: {e}")
        traceback.print_exc()
        return {"status": "error", "details": str(e)}


def get_unfinished_scan() -> Optional[tuple]:
    """
    从domain_ip_mapping表中获取有scan_id但扫描结果为空的记录
    
    Returns:
        Optional[tuple]: (domain, scan_id) 如果存在，否则返回None
    """
    try:
        db = get_db_connection()
        cursor = db.cursor()
        query = """
                SELECT domain, awvs_scan_id 
                FROM domain_ip_mapping 
                WHERE awvs_scan_id IS NOT NULL 
                AND (awvs_result IS NULL OR awvs_result = '')
                AND awvs_status = 'scanning'
                LIMIT 1
                """
        cursor.execute(query)
        result = cursor.fetchone()
        cursor.close()
        db.close()
        if result:
            print(f'发现未完成的扫描任务: 域名={result[0]}, 扫描ID={result[1]}')
            return result
        return None
    except Exception as e:
        print(f"获取未完成扫描任务时出错: {e}")
        traceback.print_exc()
        return None


def check_scan_status(domain: str, scan_id: str) -> Dict[str, Any]:
    """
    检查已有扫描任务的状态并获取结果
    
    Args:
        domain (str): 域名
        scan_id (str): 扫描ID
        
    Returns:
        Dict[str, Any]: 扫描结果
    """
    print(f"检查扫描任务状态: 域名={domain}, 扫描ID={scan_id}")
    
    # 从配置文件中读取AWVS API配置
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
    config.read(config_path, encoding='utf-8')
    
    # 获取AWVS配置
    awvs_api_url = config['awvs']['api_url']
    awvs_api_key = config['awvs']['api_key']
    
    headers = {
        'X-Auth': awvs_api_key,
        'Content-Type': 'application/json'
    }
    
    try:
        # 创建会话并禁用代理
        session = requests.Session()
        session.proxies = {}  # 清除所有代理设置
        session.verify = False  # 禁用SSL验证

        # 禁用SSL密钥日志记录以避免权限问题
        import ssl
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # 设置环境变量以禁用SSL密钥日志
        import os
        if 'SSLKEYLOGFILE' in os.environ:
            del os.environ['SSLKEYLOGFILE']

        # 获取扫描状态
        scan_status_url = f"{awvs_api_url}/api/v1/scans/{scan_id}"
        response = session.get(scan_status_url, headers=headers, timeout=30)
        response.raise_for_status()
        status = response.json().get('current_session', {}).get('status')
        
        if status == 'completed':
            print(f"扫描已完成，获取结果")
            # 获取扫描结果
            results_url = f"{awvs_api_url}/api/v1/scans/{scan_id}/results"
            response = session.get(results_url, headers=headers)
            response.raise_for_status()
            
            return {
                "status": "completed",
                "scan_id": scan_id,
                "results": response.json()
            }
        elif status in ['failed', 'aborted']:
            return {"status": "error", "details": f"扫描失败，状态: {status}"}
        else:
            print(f"扫描进行中，当前状态: {status}，等待30秒...")
            # 继续等待扫描完成
            while True:
                time.sleep(30)
                response = session.get(scan_status_url, headers=headers)
                response.raise_for_status()
                status = response.json().get('current_session', {}).get('status')
                
                if status == 'completed':
                    # 获取扫描结果
                    results_url = f"{awvs_api_url}/api/v1/scans/{scan_id}/results"
                    response = session.get(results_url, headers=headers)
                    response.raise_for_status()
                    
                    return {
                        "status": "completed",
                        "scan_id": scan_id,
                        "results": response.json()
                    }
                elif status in ['failed', 'aborted']:
                    return {"status": "error", "details": f"扫描失败，状态: {status}"}
                
                print(f"扫描进行中，当前状态: {status}，等待30秒...")
    
    except requests.exceptions.RequestException as e:
        print(f"检查扫描状态时出错: {e}")
        traceback.print_exc()
        return {"status": "error", "details": str(e)}


def main():
    """主函数，执行AWVS扫描流程"""

    # 先检查是否有未完成的扫描任务
    unfinished_scan = get_unfinished_scan()
    if unfinished_scan:
        domain, scan_id = unfinished_scan
        print(f"发现未完成的扫描任务，域名: {domain}, 扫描ID: {scan_id}")
        
        # 检查扫描状态并获取结果
        scan_result = check_scan_status(domain, scan_id)
        
        # 更新域名状态为done
        update_domain_status(domain, 'done')
        
        # 保存扫描结果
        save_scan_result(domain, scan_result)
        print(f"完成对未完成任务 {domain} 的处理")
        

    while True:
        print('开始AWVS扫描流程')

        # 获取新的待扫描域名
        domain = get_todo_domain()
        if not domain:
            print("没有待扫描的域名，扫描结束")
            break
        
        # 执行AWVS扫描
        scan_result = perform_awvs_scan(domain)
        
        # 更新域名状态为done
        update_domain_status(domain, 'done')
        
        # 保存扫描结果
        save_scan_result(domain, scan_result)
        print(f"完成对 {domain} 的AWVS扫描并保存结果")


main()