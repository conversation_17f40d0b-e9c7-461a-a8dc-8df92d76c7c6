"""
数据库存储管理器

功能：
1. 统一管理所有信息收集模块的数据存储
2. 确保数据完整性和一致性
3. 提供标准化的存储接口
4. 支持批量存储和事务处理
5. 数据去重和更新策略

作者：AI渗透测试系统
版本：1.0.0
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
import logging
import pymysql
import configparser
from contextlib import contextmanager

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

logger = logging.getLogger(__name__)


class DatabaseStorageManager:
    """数据库存储管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化存储管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path or self._get_default_config_path()
        self.db_config = self._load_db_config()
        
        logger.info("数据库存储管理器初始化完成")
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        return os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
    
    def _load_db_config(self) -> Dict[str, Any]:
        """加载数据库配置"""
        try:
            config = configparser.ConfigParser()
            config.read(self.config_path, encoding='utf-8')
            
            return {
                'host': config['mysql']['host'],
                'port': int(config['mysql']['port']),
                'user': config['mysql']['user'],
                'password': config['mysql']['password'],
                'database': config['mysql']['database'],
                'charset': config.get('mysql', 'charset', fallback='utf8mb4'),
                'connect_timeout': 60,
                'read_timeout': 300,
                'write_timeout': 300,
                'autocommit': False
            }
        except Exception as e:
            logger.error(f"加载数据库配置失败：{e}")
            raise
    
    @contextmanager
    def get_db_connection(self):
        """获取数据库连接上下文管理器"""
        connection = None
        try:
            connection = pymysql.connect(**self.db_config)
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"数据库连接错误：{e}")
            raise
        finally:
            if connection:
                connection.close()
    
    def store_target_asset(self, asset_data: Dict[str, Any]) -> Optional[int]:
        """
        存储目标资产信息
        
        Args:
            asset_data: 资产数据
            
        Returns:
            Optional[int]: 资产ID，失败返回None
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 检查资产是否已存在
                check_query = """
                SELECT id FROM target_assets 
                WHERE asset_type = %s AND asset_value = %s
                """
                cursor.execute(check_query, (asset_data['asset_type'], asset_data['asset_value']))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有资产
                    asset_id = existing[0]
                    update_query = """
                    UPDATE target_assets SET
                        asset_name = %s,
                        description = %s,
                        business_importance = %s,
                        last_scan_time = %s,
                        status = %s,
                        confidence_score = %s,
                        metadata = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    cursor.execute(update_query, (
                        asset_data.get('asset_name'),
                        asset_data.get('description'),
                        asset_data.get('business_importance', 'medium'),
                        asset_data.get('last_scan_time'),
                        asset_data.get('status', 'active'),
                        asset_data.get('confidence_score', 0.0),
                        json.dumps(asset_data.get('metadata', {}), ensure_ascii=False),
                        asset_id
                    ))
                else:
                    # 插入新资产
                    insert_query = """
                    INSERT INTO target_assets (
                        asset_type, asset_value, asset_name, description,
                        business_importance, last_scan_time, status,
                        confidence_score, metadata
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_query, (
                        asset_data['asset_type'],
                        asset_data['asset_value'],
                        asset_data.get('asset_name'),
                        asset_data.get('description'),
                        asset_data.get('business_importance', 'medium'),
                        asset_data.get('last_scan_time'),
                        asset_data.get('status', 'active'),
                        asset_data.get('confidence_score', 0.0),
                        json.dumps(asset_data.get('metadata', {}), ensure_ascii=False)
                    ))
                    asset_id = cursor.lastrowid
                
                conn.commit()
                cursor.close()
                
                logger.info(f"成功存储资产：{asset_data['asset_value']} (ID: {asset_id})")
                return asset_id
                
        except Exception as e:
            logger.error(f"存储目标资产失败：{e}")
            return None
    
    def store_network_info(self, asset_id: int, network_data: Dict[str, Any]) -> bool:
        """
        存储网络信息
        
        Args:
            asset_id: 资产ID
            network_data: 网络数据
            
        Returns:
            bool: 存储是否成功
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在相同IP的记录
                check_query = """
                SELECT id FROM network_info 
                WHERE asset_id = %s AND ip_address = %s
                """
                cursor.execute(check_query, (asset_id, network_data['ip_address']))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录
                    update_query = """
                    UPDATE network_info SET
                        mac_address = %s, hostname = %s, os_type = %s,
                        os_version = %s, os_accuracy = %s, network_segment = %s,
                        gateway = %s, response_time = %s, ttl = %s,
                        last_seen = %s, scan_method = %s, scan_data = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    cursor.execute(update_query, (
                        network_data.get('mac_address'),
                        network_data.get('hostname'),
                        network_data.get('os_type'),
                        network_data.get('os_version'),
                        network_data.get('os_accuracy', 0),
                        network_data.get('network_segment'),
                        network_data.get('gateway'),
                        network_data.get('response_time'),
                        network_data.get('ttl'),
                        network_data.get('last_seen', datetime.now()),
                        network_data.get('scan_method'),
                        json.dumps(network_data.get('scan_data', {}), ensure_ascii=False),
                        existing[0]
                    ))
                else:
                    # 插入新记录
                    insert_query = """
                    INSERT INTO network_info (
                        asset_id, ip_address, mac_address, hostname, os_type,
                        os_version, os_accuracy, network_segment, gateway,
                        response_time, ttl, last_seen, scan_method, scan_data
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_query, (
                        asset_id,
                        network_data['ip_address'],
                        network_data.get('mac_address'),
                        network_data.get('hostname'),
                        network_data.get('os_type'),
                        network_data.get('os_version'),
                        network_data.get('os_accuracy', 0),
                        network_data.get('network_segment'),
                        network_data.get('gateway'),
                        network_data.get('response_time'),
                        network_data.get('ttl'),
                        network_data.get('last_seen', datetime.now()),
                        network_data.get('scan_method'),
                        json.dumps(network_data.get('scan_data', {}), ensure_ascii=False)
                    ))
                
                conn.commit()
                cursor.close()
                
                logger.info(f"成功存储网络信息：{network_data['ip_address']}")
                return True
                
        except Exception as e:
            logger.error(f"存储网络信息失败：{e}")
            return False
    
    def store_service_info(self, asset_id: int, service_data: Dict[str, Any]) -> bool:
        """
        存储服务信息
        
        Args:
            asset_id: 资产ID
            service_data: 服务数据
            
        Returns:
            bool: 存储是否成功
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在相同服务的记录
                check_query = """
                SELECT id FROM service_info 
                WHERE asset_id = %s AND ip_address = %s AND port = %s AND protocol = %s
                """
                cursor.execute(check_query, (
                    asset_id, 
                    service_data['ip_address'], 
                    service_data['port'], 
                    service_data['protocol']
                ))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录
                    update_query = """
                    UPDATE service_info SET
                        service_name = %s, service_version = %s, service_product = %s,
                        service_banner = %s, service_extrainfo = %s, state = %s,
                        confidence_score = %s, last_verified = %s, service_data = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    cursor.execute(update_query, (
                        service_data.get('service_name'),
                        service_data.get('service_version'),
                        service_data.get('service_product'),
                        service_data.get('service_banner'),
                        service_data.get('service_extrainfo'),
                        service_data.get('state', 'open'),
                        service_data.get('confidence_score', 0.0),
                        service_data.get('last_verified', datetime.now()),
                        json.dumps(service_data.get('service_data', {}), ensure_ascii=False),
                        existing[0]
                    ))
                else:
                    # 插入新记录
                    insert_query = """
                    INSERT INTO service_info (
                        asset_id, ip_address, port, protocol, service_name,
                        service_version, service_product, service_banner,
                        service_extrainfo, state, confidence_score,
                        discovery_time, last_verified, service_data
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_query, (
                        asset_id,
                        service_data['ip_address'],
                        service_data['port'],
                        service_data['protocol'],
                        service_data.get('service_name'),
                        service_data.get('service_version'),
                        service_data.get('service_product'),
                        service_data.get('service_banner'),
                        service_data.get('service_extrainfo'),
                        service_data.get('state', 'open'),
                        service_data.get('confidence_score', 0.0),
                        service_data.get('discovery_time', datetime.now()),
                        service_data.get('last_verified', datetime.now()),
                        json.dumps(service_data.get('service_data', {}), ensure_ascii=False)
                    ))
                
                conn.commit()
                cursor.close()
                
                logger.info(f"成功存储服务信息：{service_data['ip_address']}:{service_data['port']}")
                return True
                
        except Exception as e:
            logger.error(f"存储服务信息失败：{e}")
            return False
    
    def store_web_app_info(self, asset_id: int, web_data: Dict[str, Any]) -> bool:
        """
        存储Web应用信息
        
        Args:
            asset_id: 资产ID
            web_data: Web应用数据
            
        Returns:
            bool: 存储是否成功
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在相同URL的记录
                check_query = """
                SELECT id FROM web_app_info 
                WHERE asset_id = %s AND url = %s
                """
                cursor.execute(check_query, (asset_id, web_data['url']))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录
                    update_query = """
                    UPDATE web_app_info SET
                        domain = %s, ip_address = %s, port = %s, protocol = %s,
                        title = %s, description = %s, keywords = %s, server = %s,
                        powered_by = %s, cms_type = %s, cms_version = %s,
                        technologies = %s, response_code = %s, content_length = %s,
                        content_type = %s, charset = %s, language = %s,
                        last_modified = %s, last_crawled = %s, web_data = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    cursor.execute(update_query, (
                        web_data.get('domain'),
                        web_data.get('ip_address'),
                        web_data.get('port', 80),
                        web_data.get('protocol', 'http'),
                        web_data.get('title'),
                        web_data.get('description'),
                        web_data.get('keywords'),
                        web_data.get('server'),
                        web_data.get('powered_by'),
                        web_data.get('cms_type'),
                        web_data.get('cms_version'),
                        json.dumps(web_data.get('technologies', []), ensure_ascii=False),
                        web_data.get('response_code'),
                        web_data.get('content_length'),
                        web_data.get('content_type'),
                        web_data.get('charset'),
                        web_data.get('language'),
                        web_data.get('last_modified'),
                        web_data.get('last_crawled', datetime.now()),
                        json.dumps(web_data.get('web_data', {}), ensure_ascii=False),
                        existing[0]
                    ))
                else:
                    # 插入新记录
                    insert_query = """
                    INSERT INTO web_app_info (
                        asset_id, url, domain, ip_address, port, protocol,
                        title, description, keywords, server, powered_by,
                        cms_type, cms_version, technologies, response_code,
                        content_length, content_type, charset, language,
                        last_modified, discovery_time, last_crawled, web_data
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_query, (
                        asset_id,
                        web_data['url'],
                        web_data.get('domain'),
                        web_data.get('ip_address'),
                        web_data.get('port', 80),
                        web_data.get('protocol', 'http'),
                        web_data.get('title'),
                        web_data.get('description'),
                        web_data.get('keywords'),
                        web_data.get('server'),
                        web_data.get('powered_by'),
                        web_data.get('cms_type'),
                        web_data.get('cms_version'),
                        json.dumps(web_data.get('technologies', []), ensure_ascii=False),
                        web_data.get('response_code'),
                        web_data.get('content_length'),
                        web_data.get('content_type'),
                        web_data.get('charset'),
                        web_data.get('language'),
                        web_data.get('last_modified'),
                        web_data.get('discovery_time', datetime.now()),
                        web_data.get('last_crawled', datetime.now()),
                        json.dumps(web_data.get('web_data', {}), ensure_ascii=False)
                    ))
                
                conn.commit()
                cursor.close()
                
                logger.info(f"成功存储Web应用信息：{web_data['url']}")
                return True
                
        except Exception as e:
            logger.error(f"存储Web应用信息失败：{e}")
            return False

    def store_vulnerability_info(self, asset_id: int, vuln_data: Dict[str, Any]) -> bool:
        """
        存储漏洞信息

        Args:
            asset_id: 资产ID
            vuln_data: 漏洞数据

        Returns:
            bool: 存储是否成功
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # 检查是否已存在相同漏洞的记录
                check_query = """
                SELECT id FROM vulnerability_info
                WHERE asset_id = %s AND vuln_id = %s AND vuln_type = %s
                """
                cursor.execute(check_query, (
                    asset_id,
                    vuln_data.get('vuln_id', ''),
                    vuln_data['vuln_type']
                ))
                existing = cursor.fetchone()

                if existing:
                    # 更新现有记录
                    update_query = """
                    UPDATE vulnerability_info SET
                        vuln_name = %s, severity = %s, cvss_score = %s,
                        cve_id = %s, description = %s, solution = %s,
                        vuln_references = %s, exploit_available = %s, verified = %s,
                        false_positive = %s, last_verified = %s, vuln_data = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    cursor.execute(update_query, (
                        vuln_data.get('vuln_name'),
                        vuln_data.get('severity', 'unknown'),
                        vuln_data.get('cvss_score', 0.0),
                        vuln_data.get('cve_id'),
                        vuln_data.get('description'),
                        vuln_data.get('solution'),
                        json.dumps(vuln_data.get('references', []), ensure_ascii=False),
                        vuln_data.get('exploit_available', False),
                        vuln_data.get('verified', False),
                        vuln_data.get('false_positive', False),
                        vuln_data.get('last_verified', datetime.now()),
                        json.dumps(vuln_data.get('vuln_data', {}), ensure_ascii=False),
                        existing[0]
                    ))
                else:
                    # 插入新记录
                    insert_query = """
                    INSERT INTO vulnerability_info (
                        asset_id, vuln_id, vuln_type, vuln_name, severity,
                        cvss_score, cve_id, description, solution, vuln_references,
                        exploit_available, verified, false_positive,
                        discovery_time, last_verified, vuln_data
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_query, (
                        asset_id,
                        vuln_data.get('vuln_id', ''),
                        vuln_data['vuln_type'],
                        vuln_data.get('vuln_name'),
                        vuln_data.get('severity', 'unknown'),
                        vuln_data.get('cvss_score', 0.0),
                        vuln_data.get('cve_id'),
                        vuln_data.get('description'),
                        vuln_data.get('solution'),
                        json.dumps(vuln_data.get('references', []), ensure_ascii=False),
                        vuln_data.get('exploit_available', False),
                        vuln_data.get('verified', False),
                        vuln_data.get('false_positive', False),
                        vuln_data.get('discovery_time', datetime.now()),
                        vuln_data.get('last_verified', datetime.now()),
                        json.dumps(vuln_data.get('vuln_data', {}), ensure_ascii=False)
                    ))

                conn.commit()
                cursor.close()

                logger.info(f"成功存储漏洞信息：{vuln_data.get('vuln_name', 'Unknown')}")
                return True

        except Exception as e:
            logger.error(f"存储漏洞信息失败：{e}")
            return False

    def store_database_info(self, asset_id: int, db_data: Dict[str, Any]) -> bool:
        """
        存储数据库信息

        Args:
            asset_id: 资产ID
            db_data: 数据库数据

        Returns:
            bool: 存储是否成功
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # 将数据库信息存储为服务信息的特殊类型
                service_data = {
                    'ip_address': db_data.get('host', ''),
                    'port': db_data.get('port', 0),
                    'protocol': 'tcp',
                    'service_name': db_data.get('type', 'database'),
                    'service_version': db_data.get('version', ''),
                    'service_product': db_data.get('type', ''),
                    'service_banner': db_data.get('banner', ''),
                    'state': 'open',
                    'confidence_score': 1.0,
                    'service_data': {
                        'database_type': db_data.get('type'),
                        'authentication': db_data.get('authentication', {}),
                        'databases': db_data.get('databases', []),
                        'users': db_data.get('users', []),
                        'configuration': db_data.get('configuration', {}),
                        'ssl_enabled': db_data.get('ssl_enabled', False),
                        'vulnerabilities': db_data.get('vulnerabilities', []),
                        'scan_time': db_data.get('scan_time')
                    }
                }

                # 存储为服务信息
                success = self.store_service_info(asset_id, service_data)

                # 如果有漏洞信息，单独存储
                if db_data.get('vulnerabilities'):
                    for vuln in db_data['vulnerabilities']:
                        vuln_data = {
                            'vuln_type': 'database',
                            'vuln_name': vuln.get('description', 'Database Vulnerability'),
                            'severity': vuln.get('severity', 'unknown'),
                            'cve_id': vuln.get('cve'),
                            'description': vuln.get('description'),
                            'solution': vuln.get('recommendation'),
                            'vuln_data': vuln
                        }
                        self.store_vulnerability_info(asset_id, vuln_data)

                logger.info(f"成功存储数据库信息：{db_data.get('type')} on {db_data.get('host')}:{db_data.get('port')}")
                return success

        except Exception as e:
            logger.error(f"存储数据库信息失败：{e}")
            return False

    def store_threat_intelligence(self, asset_id: int, threat_data: Dict[str, Any]) -> bool:
        """
        存储威胁情报信息

        Args:
            asset_id: 资产ID
            threat_data: 威胁情报数据

        Returns:
            bool: 存储是否成功
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # 检查是否已存在相同威胁情报的记录
                check_query = """
                SELECT id FROM threat_intelligence
                WHERE asset_id = %s AND indicator_value = %s AND indicator_type = %s
                """
                cursor.execute(check_query, (
                    asset_id,
                    threat_data['indicator_value'],
                    threat_data['indicator_type']
                ))
                existing = cursor.fetchone()

                if existing:
                    # 更新现有记录
                    update_query = """
                    UPDATE threat_intelligence SET
                        threat_type = %s, confidence_score = %s, severity = %s,
                        source = %s, description = %s, first_seen = %s,
                        last_seen = %s, ioc_data = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    cursor.execute(update_query, (
                        threat_data.get('threat_type'),
                        threat_data.get('confidence_score', 0.0),
                        threat_data.get('severity', 'unknown'),
                        threat_data.get('source'),
                        threat_data.get('description'),
                        threat_data.get('first_seen'),
                        threat_data.get('last_seen', datetime.now()),
                        json.dumps(threat_data.get('ioc_data', {}), ensure_ascii=False),
                        existing[0]
                    ))
                else:
                    # 插入新记录
                    insert_query = """
                    INSERT INTO threat_intelligence (
                        asset_id, indicator_type, indicator_value, threat_type,
                        confidence_score, severity, source, description,
                        first_seen, last_seen, discovery_time, ioc_data
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_query, (
                        asset_id,
                        threat_data['indicator_type'],
                        threat_data['indicator_value'],
                        threat_data.get('threat_type'),
                        threat_data.get('confidence_score', 0.0),
                        threat_data.get('severity', 'unknown'),
                        threat_data.get('source'),
                        threat_data.get('description'),
                        threat_data.get('first_seen'),
                        threat_data.get('last_seen', datetime.now()),
                        threat_data.get('discovery_time', datetime.now()),
                        json.dumps(threat_data.get('ioc_data', {}), ensure_ascii=False)
                    ))

                conn.commit()
                cursor.close()

                logger.info(f"成功存储威胁情报：{threat_data['indicator_value']}")
                return True

        except Exception as e:
            logger.error(f"存储威胁情报失败：{e}")
            return False

    def store_comprehensive_scan_results(self, scan_results: Dict[str, Any]) -> bool:
        """
        存储综合扫描结果

        Args:
            scan_results: 综合扫描结果

        Returns:
            bool: 存储是否成功
        """
        try:
            target = scan_results.get('target', '')
            if not target:
                logger.error("扫描结果中缺少目标信息")
                return False

            # 1. 存储目标资产
            asset_data = {
                'asset_type': 'ip' if self._is_ip_address(target) else 'domain',
                'asset_value': target,
                'asset_name': scan_results.get('target_name', target),
                'description': f"综合扫描目标 - {scan_results.get('scan_time', '')}",
                'business_importance': scan_results.get('business_importance', 'medium'),
                'last_scan_time': datetime.now(),
                'status': 'active',
                'confidence_score': scan_results.get('confidence_score', 1.0),
                'metadata': {
                    'scan_type': 'comprehensive',
                    'scan_time': scan_results.get('scan_time'),
                    'scan_duration': scan_results.get('scan_duration'),
                    'scanner_version': scan_results.get('scanner_version', '1.0.0')
                }
            }

            asset_id = self.store_target_asset(asset_data)
            if not asset_id:
                logger.error("存储目标资产失败")
                return False

            # 2. 存储网络信息
            if scan_results.get('network_info'):
                network_data = scan_results['network_info'].copy()
                network_data['ip_address'] = target if self._is_ip_address(target) else scan_results.get('resolved_ip', target)
                network_data['scan_method'] = 'comprehensive'
                network_data['scan_data'] = scan_results.get('network_info', {})
                self.store_network_info(asset_id, network_data)

            # 3. 存储服务信息
            if scan_results.get('services'):
                for service in scan_results['services']:
                    service_data = service.copy()
                    service_data['asset_id'] = asset_id
                    service_data['ip_address'] = target if self._is_ip_address(target) else scan_results.get('resolved_ip', target)
                    self.store_service_info(asset_id, service_data)

            # 4. 存储Web应用信息
            if scan_results.get('web_apps'):
                for web_app in scan_results['web_apps']:
                    web_data = web_app.copy()
                    web_data['asset_id'] = asset_id
                    self.store_web_app_info(asset_id, web_data)

            # 5. 存储数据库信息
            if scan_results.get('databases'):
                for database in scan_results['databases']:
                    db_data = database.copy()
                    self.store_database_info(asset_id, db_data)

            # 6. 存储漏洞信息
            if scan_results.get('vulnerabilities'):
                for vulnerability in scan_results['vulnerabilities']:
                    vuln_data = vulnerability.copy()
                    self.store_vulnerability_info(asset_id, vuln_data)

            # 7. 存储威胁情报
            if scan_results.get('threat_intelligence'):
                for threat in scan_results['threat_intelligence']:
                    threat_data = threat.copy()
                    self.store_threat_intelligence(asset_id, threat_data)

            logger.info(f"成功存储综合扫描结果：{target} (资产ID: {asset_id})")
            return True

        except Exception as e:
            logger.error(f"存储综合扫描结果失败：{e}")
            return False

    def store_awvs_scan_results(self, domain: str, scan_results: Dict[str, Any]) -> bool:
        """
        存储AWVS扫描结果

        Args:
            domain: 域名
            scan_results: AWVS扫描结果

        Returns:
            bool: 存储是否成功
        """
        try:
            # 1. 存储或更新目标资产
            asset_data = {
                'asset_type': 'domain',
                'asset_value': domain,
                'asset_name': domain,
                'description': f"AWVS扫描目标 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                'last_scan_time': datetime.now(),
                'status': 'active',
                'metadata': {
                    'scan_type': 'awvs',
                    'scan_status': scan_results.get('status'),
                    'target_id': scan_results.get('target_id'),
                    'scan_id': scan_results.get('scan_id')
                }
            }

            asset_id = self.store_target_asset(asset_data)
            if not asset_id:
                logger.error("存储AWVS目标资产失败")
                return False

            # 2. 存储漏洞信息
            if scan_results.get('results') and scan_results['results'].get('vulnerabilities'):
                for vuln in scan_results['results']['vulnerabilities']:
                    vuln_data = {
                        'vuln_type': 'web',
                        'vuln_name': vuln.get('vuln_name', 'Web Vulnerability'),
                        'severity': vuln.get('severity', 'unknown'),
                        'cvss_score': vuln.get('cvss_score', 0.0),
                        'description': vuln.get('description'),
                        'solution': vuln.get('recommendation'),
                        'verified': True,
                        'vuln_data': vuln
                    }
                    self.store_vulnerability_info(asset_id, vuln_data)

            # 3. 更新domain_ip_mapping表（保持兼容性）
            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # 检查是否存在记录
                check_query = "SELECT id FROM domain_ip_mapping WHERE domain = %s"
                cursor.execute(check_query, (domain,))
                existing = cursor.fetchone()

                result_json = json.dumps(scan_results, ensure_ascii=False)

                if existing:
                    # 更新现有记录
                    update_query = """
                    UPDATE domain_ip_mapping
                    SET awvs_result = %s, awvs_status = 'done', updated_at = CURRENT_TIMESTAMP
                    WHERE domain = %s
                    """
                    cursor.execute(update_query, (result_json, domain))
                else:
                    # 插入新记录，提供默认的ip_address
                    insert_query = """
                    INSERT INTO domain_ip_mapping (domain, ip_address, awvs_result, awvs_status)
                    VALUES (%s, %s, %s, 'done')
                    """
                    cursor.execute(insert_query, (domain, '0.0.0.0', result_json))

                conn.commit()
                cursor.close()

            logger.info(f"成功存储AWVS扫描结果：{domain}")
            return True

        except Exception as e:
            logger.error(f"存储AWVS扫描结果失败：{e}")
            return False

    def batch_store_scan_results(self, scan_results_list: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        批量存储扫描结果

        Args:
            scan_results_list: 扫描结果列表

        Returns:
            Dict[str, int]: 存储统计信息
        """
        stats = {
            'total': len(scan_results_list),
            'success': 0,
            'failed': 0,
            'errors': []
        }

        for i, scan_result in enumerate(scan_results_list):
            try:
                if scan_result.get('scan_type') == 'awvs':
                    success = self.store_awvs_scan_results(
                        scan_result.get('domain', ''),
                        scan_result
                    )
                else:
                    success = self.store_comprehensive_scan_results(scan_result)

                if success:
                    stats['success'] += 1
                else:
                    stats['failed'] += 1
                    stats['errors'].append(f"第{i+1}个结果存储失败")

            except Exception as e:
                stats['failed'] += 1
                stats['errors'].append(f"第{i+1}个结果存储异常：{str(e)}")
                logger.error(f"批量存储第{i+1}个结果失败：{e}")

        logger.info(f"批量存储完成：成功{stats['success']}个，失败{stats['failed']}个")
        return stats

    def _is_ip_address(self, value: str) -> bool:
        """检查字符串是否为IP地址"""
        import re
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        return bool(re.match(ip_pattern, value))

    def get_asset_statistics(self) -> Dict[str, Any]:
        """
        获取资产统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                stats = {}

                # 资产统计
                cursor.execute("SELECT asset_type, COUNT(*) FROM target_assets GROUP BY asset_type")
                asset_counts = dict(cursor.fetchall())
                stats['assets'] = asset_counts

                # 服务统计
                cursor.execute("SELECT service_name, COUNT(*) FROM service_info GROUP BY service_name ORDER BY COUNT(*) DESC LIMIT 10")
                service_counts = dict(cursor.fetchall())
                stats['top_services'] = service_counts

                # 漏洞统计
                cursor.execute("SELECT severity, COUNT(*) FROM vulnerability_info GROUP BY severity")
                vuln_counts = dict(cursor.fetchall())
                stats['vulnerabilities'] = vuln_counts

                # Web应用统计
                cursor.execute("SELECT cms_type, COUNT(*) FROM web_app_info WHERE cms_type IS NOT NULL GROUP BY cms_type ORDER BY COUNT(*) DESC LIMIT 10")
                cms_counts = dict(cursor.fetchall())
                stats['top_cms'] = cms_counts

                cursor.close()
                return stats

        except Exception as e:
            logger.error(f"获取资产统计信息失败：{e}")
            return {}


# 使用示例
if __name__ == "__main__":
    # 创建存储管理器
    storage_manager = DatabaseStorageManager()

    # 示例：存储综合扫描结果
    sample_scan_result = {
        'target': '*************',
        'scan_time': datetime.now().isoformat(),
        'scan_duration': 120.5,
        'network_info': {
            'hostname': 'test-server',
            'os_type': 'Linux',
            'response_time': 10
        },
        'services': [
            {
                'port': 80,
                'protocol': 'tcp',
                'service_name': 'http',
                'service_version': 'Apache 2.4.41'
            }
        ],
        'vulnerabilities': [
            {
                'vuln_type': 'web',
                'vuln_name': 'SQL Injection',
                'severity': 'high',
                'description': 'SQL injection vulnerability found'
            }
        ]
    }

    # 存储结果
    success = storage_manager.store_comprehensive_scan_results(sample_scan_result)
    print(f"存储结果：{'成功' if success else '失败'}")

    # 获取统计信息
    stats = storage_manager.get_asset_statistics()
    print(f"资产统计：{stats}")
