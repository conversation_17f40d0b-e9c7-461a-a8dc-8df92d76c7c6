"""
数据库扫描集成模块

功能：
1. 集成数据库扫描到主系统
2. 数据库扫描结果存储
3. 扫描报告生成
4. 与其他模块的接口

作者：AI渗透测试系统
版本：1.0.0
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from info_collector.database.database_scanner import DatabaseScanner, DatabaseVulnerabilityScanner
from info_collector.database.database_config import (
    DatabaseScanConfig, get_default_config, get_all_database_ports,
    REPORT_TEMPLATE
)

logger = logging.getLogger(__name__)


class DatabaseScanIntegrator:
    """数据库扫描集成器"""
    
    def __init__(self, config: Optional[DatabaseScanConfig] = None):
        """
        初始化数据库扫描集成器
        
        Args:
            config: 扫描配置，如果为None则使用默认配置
        """
        self.config = config or get_default_config()
        self.scanner = DatabaseScanner()
        self.vuln_scanner = DatabaseVulnerabilityScanner()
        
        logger.info("数据库扫描集成器初始化完成")
    
    def scan_target(self, target: str, ports: Optional[List[int]] = None) -> Dict[str, Any]:
        """
        扫描目标的数据库服务
        
        Args:
            target: 目标IP或域名
            ports: 指定端口列表，如果为None则扫描所有常见数据库端口
            
        Returns:
            Dict: 完整的扫描结果
        """
        logger.info(f"开始数据库扫描：{target}")
        start_time = time.time()
        
        # 如果没有指定端口，使用配置中的端口或所有数据库端口
        if ports is None:
            if self.config.target_ports:
                ports = self.config.target_ports
            else:
                ports = get_all_database_ports()
        
        # 执行基础数据库扫描
        scan_results = self.scanner.scan_database_services(target, ports)
        
        # 执行漏洞扫描
        if self.config.check_injection_vulnerabilities:
            for db_info in scan_results.get('discovered_databases', []):
                try:
                    injection_vulns = self.vuln_scanner.scan_sql_injection_vulnerabilities(target, db_info)
                    scan_results['vulnerabilities'].extend(injection_vulns)
                except Exception as e:
                    logger.error(f"SQL注入扫描失败：{e}")
        
        # 计算扫描时间
        scan_duration = time.time() - start_time
        scan_results['scan_duration'] = scan_duration
        
        # 生成报告
        if self.config.generate_report:
            report = self._generate_scan_report(scan_results)
            scan_results['report'] = report
        
        # 保存结果
        if self.config.save_raw_results:
            self._save_scan_results(target, scan_results)
        
        logger.info(f"数据库扫描完成，耗时：{scan_duration:.2f}秒")
        return scan_results
    
    def scan_multiple_targets(self, targets: List[str]) -> Dict[str, Any]:
        """
        扫描多个目标
        
        Args:
            targets: 目标列表
            
        Returns:
            Dict: 所有目标的扫描结果
        """
        logger.info(f"开始批量数据库扫描，目标数量：{len(targets)}")
        
        all_results = {
            'scan_time': datetime.now().isoformat(),
            'targets': targets,
            'results': {},
            'summary': {
                'total_targets': len(targets),
                'successful_scans': 0,
                'failed_scans': 0,
                'total_databases_found': 0,
                'total_vulnerabilities': 0
            }
        }
        
        for target in targets:
            try:
                result = self.scan_target(target)
                all_results['results'][target] = result
                all_results['summary']['successful_scans'] += 1
                all_results['summary']['total_databases_found'] += len(result.get('discovered_databases', []))
                all_results['summary']['total_vulnerabilities'] += len(result.get('vulnerabilities', []))
                
            except Exception as e:
                logger.error(f"扫描目标失败 {target}：{e}")
                all_results['results'][target] = {'error': str(e)}
                all_results['summary']['failed_scans'] += 1
        
        return all_results
    
    def _generate_scan_report(self, scan_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成扫描报告"""
        report = REPORT_TEMPLATE.copy()
        
        # 填充扫描信息
        report['scan_info'].update({
            'scan_time': scan_results.get('scan_time'),
            'target': scan_results.get('target'),
            'scan_duration': scan_results.get('scan_duration')
        })
        
        # 统计信息
        databases = scan_results.get('discovered_databases', [])
        vulnerabilities = scan_results.get('vulnerabilities', [])
        
        report['summary'].update({
            'total_databases_found': len(databases),
            'total_vulnerabilities': len(vulnerabilities),
            'critical_vulnerabilities': len([v for v in vulnerabilities if v.get('severity') == 'critical']),
            'high_vulnerabilities': len([v for v in vulnerabilities if v.get('severity') == 'high']),
            'medium_vulnerabilities': len([v for v in vulnerabilities if v.get('severity') == 'medium']),
            'low_vulnerabilities': len([v for v in vulnerabilities if v.get('severity') == 'low'])
        })
        
        # 数据库详情
        report['databases'] = databases
        report['vulnerabilities'] = vulnerabilities
        report['recommendations'] = scan_results.get('recommendations', [])
        
        return report
    
    def _save_scan_results(self, target: str, results: Dict[str, Any]):
        """保存扫描结果"""
        try:
            # 创建结果目录
            results_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'results', 'database_scans')
            os.makedirs(results_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"database_scan_{target.replace('.', '_')}_{timestamp}.json"
            filepath = os.path.join(results_dir, filename)
            
            # 保存结果
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"扫描结果已保存：{filepath}")
            
        except Exception as e:
            logger.error(f"保存扫描结果失败：{e}")
    
    def get_database_statistics(self, scan_results: Dict[str, Any]) -> Dict[str, Any]:
        """获取数据库统计信息"""
        databases = scan_results.get('discovered_databases', [])
        vulnerabilities = scan_results.get('vulnerabilities', [])
        
        # 按数据库类型统计
        db_types = {}
        for db in databases:
            db_type = db.get('type', 'unknown')
            if db_type not in db_types:
                db_types[db_type] = {
                    'count': 0,
                    'versions': [],
                    'ports': [],
                    'vulnerabilities': 0
                }
            
            db_types[db_type]['count'] += 1
            if db.get('version'):
                db_types[db_type]['versions'].append(db['version'])
            if db.get('port'):
                db_types[db_type]['ports'].append(db['port'])
        
        # 按严重程度统计漏洞
        vuln_severity = {
            'critical': 0,
            'high': 0,
            'medium': 0,
            'low': 0,
            'info': 0
        }
        
        for vuln in vulnerabilities:
            severity = vuln.get('severity', 'info')
            if severity in vuln_severity:
                vuln_severity[severity] += 1
        
        # 按类型统计漏洞
        vuln_types = {}
        for vuln in vulnerabilities:
            vuln_type = vuln.get('type', 'unknown')
            vuln_types[vuln_type] = vuln_types.get(vuln_type, 0) + 1
        
        return {
            'database_types': db_types,
            'vulnerability_severity': vuln_severity,
            'vulnerability_types': vuln_types,
            'total_databases': len(databases),
            'total_vulnerabilities': len(vulnerabilities)
        }
    
    def export_to_csv(self, scan_results: Dict[str, Any], output_file: str):
        """导出扫描结果到CSV文件"""
        try:
            import csv
            
            databases = scan_results.get('discovered_databases', [])
            vulnerabilities = scan_results.get('vulnerabilities', [])
            
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入数据库信息
                writer.writerow(['=== 发现的数据库 ==='])
                writer.writerow(['类型', '主机', '端口', '版本', '认证状态'])
                
                for db in databases:
                    auth_status = '需要认证'
                    if db.get('authentication', {}).get('anonymous_access'):
                        auth_status = '匿名访问'
                    elif db.get('authentication', {}).get('weak_credentials'):
                        auth_status = '弱密码'
                    
                    writer.writerow([
                        db.get('type', ''),
                        db.get('host', ''),
                        db.get('port', ''),
                        db.get('version', ''),
                        auth_status
                    ])
                
                # 写入漏洞信息
                writer.writerow([])
                writer.writerow(['=== 发现的漏洞 ==='])
                writer.writerow(['类型', '严重程度', '描述', 'CVE', '建议'])
                
                for vuln in vulnerabilities:
                    writer.writerow([
                        vuln.get('type', ''),
                        vuln.get('severity', ''),
                        vuln.get('description', ''),
                        vuln.get('cve', ''),
                        vuln.get('recommendation', '')
                    ])
            
            logger.info(f"扫描结果已导出到CSV：{output_file}")
            
        except Exception as e:
            logger.error(f"导出CSV失败：{e}")
    
    def generate_html_report(self, scan_results: Dict[str, Any], output_file: str):
        """生成HTML格式的扫描报告"""
        try:
            report = scan_results.get('report', self._generate_scan_report(scan_results))
            
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库安全扫描报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .database {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
        .vulnerability {{ border-left: 4px solid #ff6b6b; padding: 10px; margin: 10px 0; }}
        .critical {{ border-left-color: #ff3838; }}
        .high {{ border-left-color: #ff6b6b; }}
        .medium {{ border-left-color: #ffa726; }}
        .low {{ border-left-color: #66bb6a; }}
        .recommendations {{ background-color: #e8f5e8; padding: 15px; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>数据库安全扫描报告</h1>
        <p><strong>扫描目标：</strong>{report['scan_info']['target']}</p>
        <p><strong>扫描时间：</strong>{report['scan_info']['scan_time']}</p>
        <p><strong>扫描耗时：</strong>{report['scan_info'].get('scan_duration', 0):.2f}秒</p>
    </div>
    
    <div class="summary">
        <h2>扫描摘要</h2>
        <ul>
            <li>发现数据库：{report['summary']['total_databases_found']} 个</li>
            <li>发现漏洞：{report['summary']['total_vulnerabilities']} 个</li>
            <li>严重漏洞：{report['summary']['critical_vulnerabilities']} 个</li>
            <li>高危漏洞：{report['summary']['high_vulnerabilities']} 个</li>
            <li>中危漏洞：{report['summary']['medium_vulnerabilities']} 个</li>
            <li>低危漏洞：{report['summary']['low_vulnerabilities']} 个</li>
        </ul>
    </div>
    
    <div class="databases">
        <h2>发现的数据库</h2>
"""
            
            for db in report['databases']:
                html_content += f"""
        <div class="database">
            <h3>{db.get('type', 'Unknown')} - {db.get('host', '')}:{db.get('port', '')}</h3>
            <p><strong>版本：</strong>{db.get('version', 'Unknown')}</p>
            <p><strong>认证状态：</strong>{'匿名访问' if db.get('authentication', {}).get('anonymous_access') else '需要认证'}</p>
        </div>
"""
            
            html_content += """
    </div>
    
    <div class="vulnerabilities">
        <h2>发现的漏洞</h2>
"""
            
            for vuln in report['vulnerabilities']:
                severity_class = vuln.get('severity', 'low')
                html_content += f"""
        <div class="vulnerability {severity_class}">
            <h4>{vuln.get('description', 'Unknown Vulnerability')}</h4>
            <p><strong>类型：</strong>{vuln.get('type', '')}</p>
            <p><strong>严重程度：</strong>{vuln.get('severity', '')}</p>
            <p><strong>CVE：</strong>{vuln.get('cve', 'N/A')}</p>
            <p><strong>建议：</strong>{vuln.get('recommendation', '')}</p>
        </div>
"""
            
            html_content += f"""
    </div>
    
    <div class="recommendations">
        <h2>安全建议</h2>
        <ul>
"""
            
            for rec in report['recommendations']:
                html_content += f"            <li>{rec}</li>\n"
            
            html_content += """
        </ul>
    </div>
</body>
</html>
"""
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML报告已生成：{output_file}")
            
        except Exception as e:
            logger.error(f"生成HTML报告失败：{e}")


# 使用示例
if __name__ == "__main__":
    # 创建扫描集成器
    integrator = DatabaseScanIntegrator()
    
    # 扫描单个目标
    target = "192.168.1.100"
    results = integrator.scan_target(target)
    
    print("扫描结果摘要：")
    stats = integrator.get_database_statistics(results)
    print(json.dumps(stats, indent=2, ensure_ascii=False))
    
    # 导出结果
    integrator.export_to_csv(results, f"database_scan_{target}.csv")
    integrator.generate_html_report(results, f"database_scan_{target}.html")
