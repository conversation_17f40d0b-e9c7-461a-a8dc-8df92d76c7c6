'''
攻击路径生成器模块

功能：
1. 接收安全分析结果
2. 使用大模型生成详细的攻击路径和实现代码
3. 提供攻击成功的判断标准
4. 支持多种攻击向量和技术

输出格式：
- 攻击路径：包含名称、描述和详细步骤
- 实现代码：可执行的Python代码
- 成功指标：判断攻击是否成功的标准
'''

import os
import json
import configparser
from typing import Dict, List, Any, Optional
from openai import OpenAI


def get_llm_client() -> OpenAI:
    """获取LLM客户端"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'config.config')
    config.read(config_path, encoding='utf-8')
    
    api_key = config['llm']['api_key']
    base_url = config['llm']['base_url']
    
    return OpenAI(
        api_key=api_key,
        base_url=base_url
    )


def generate_attack_path(analysis_result: Dict[str, Any], target_info: Dict[str, Any]) -> Dict[str, Any]:
    """生成攻击路径和实现代码
    
    参数:
        analysis_result: 安全分析结果
        target_info: 目标信息，包含网络和Web信息
        
    返回:
        包含攻击路径和代码的字典
    """
    print("开始生成攻击路径...")
    try:
        client = get_llm_client()
        
        # 构建提示词
        prompt = f"""
你是一名专业的渗透测试专家，请基于以下安全分析结果和目标信息，生成详细的攻击路径和实现代码：

### 目标信息
```json
{json.dumps(target_info, indent=2)}
```

### 安全分析结果
{analysis_result['analysis']}

请提供：
1. 攻击路径：详细描述如何利用发现的漏洞进行攻击，包括每个步骤的技术细节和预期结果
2. 实现代码：提供可执行的Python代码，用于验证和利用这些漏洞
3. 成功指标：说明如何判断攻击是否成功

输出格式：
```json
{{
  "attack_paths": [
    {{
      "name": "攻击路径名称",
      "description": "详细描述",
      "risk_level": "高/中/低",
      "steps": [
        {{
          "step": 1,
          "description": "步骤描述",
          "technical_details": "技术细节"
        }},
        // 更多步骤...
      ]
    }},
    // 更多攻击路径...
  ],
  "exploit_code": {{
    "language": "python",
    "code": "完整的Python代码，包含必要的导入和注释"
  }},
  "success_criteria": "如何判断攻击是否成功的详细说明"
}}
```

请确保生成的代码是可执行的，并且包含所有必要的依赖和错误处理。
"""
        
        # 调用大模型生成攻击路径
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are a cybersecurity expert specialized in penetration testing and exploit development."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2
        )
        
        # 解析响应
        attack_path_text = response.choices[0].message.content
        
        # 提取JSON部分
        try:
            # 尝试直接解析整个文本
            attack_path = json.loads(attack_path_text)
        except json.JSONDecodeError:
            # 如果失败，尝试提取```json和```之间的内容
            import re
            json_match = re.search(r'```json\n(.+?)\n```', attack_path_text, re.DOTALL)
            if json_match:
                attack_path = json.loads(json_match.group(1))
            else:
                # 如果仍然失败，返回原始文本
                attack_path = {"raw_response": attack_path_text}
        
        return {
            "status": "success",
            "attack_path": attack_path
        }
    except Exception as e:
        print(f"生成攻击路径时出错: {e}")
        return {"status": "error", "details": str(e)}


def generate_alternative_attack_path(analysis_result: Dict[str, Any], target_info: Dict[str, Any], 
                                    failed_attempt: Dict[str, Any]) -> Dict[str, Any]:
    """生成替代攻击路径
    
    当第一次攻击失败时，生成替代的攻击路径
    
    参数:
        analysis_result: 安全分析结果
        target_info: 目标信息
        failed_attempt: 失败的攻击尝试信息
        
    返回:
        包含新攻击路径和代码的字典
    """
    print("生成替代攻击路径...")
    try:
        client = get_llm_client()
        
        # 构建提示词
        prompt = f"""
你是一名专业的渗透测试专家，之前的攻击尝试失败了，请基于以下信息生成新的攻击路径：

### 目标信息
```json
{json.dumps(target_info, indent=2)}
```

### 安全分析结果
{analysis_result['analysis']}

### 失败的攻击尝试
```json
{json.dumps(failed_attempt, indent=2)}
```

请提供一个不同的攻击路径，避免之前失败的方法，考虑其他可能的漏洞利用方式。

输出格式：
```json
{{
  "attack_paths": [
    {{
      "name": "攻击路径名称",
      "description": "详细描述",
      "risk_level": "高/中/低",
      "steps": [
        {{
          "step": 1,
          "description": "步骤描述",
          "technical_details": "技术细节"
        }},
        // 更多步骤...
      ]
    }}
  ],
  "exploit_code": {{
    "language": "python",
    "code": "完整的Python代码，包含必要的导入和注释"
  }},
  "success_criteria": "如何判断攻击是否成功的详细说明"
}}
```

请确保生成的代码是可执行的，并且包含所有必要的依赖和错误处理。
"""
        
        # 调用大模型生成攻击路径
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are a cybersecurity expert specialized in penetration testing and exploit development."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3  # 稍微提高温度以获得更多样化的结果
        )
        
        # 解析响应
        attack_path_text = response.choices[0].message.content
        
        # 提取JSON部分
        try:
            # 尝试直接解析整个文本
            attack_path = json.loads(attack_path_text)
        except json.JSONDecodeError:
            # 如果失败，尝试提取```json和```之间的内容
            import re
            json_match = re.search(r'```json\n(.+?)\n```', attack_path_text, re.DOTALL)
            if json_match:
                attack_path = json.loads(json_match.group(1))
            else:
                # 如果仍然失败，返回原始文本
                attack_path = {"raw_response": attack_path_text}
        
        return {
            "status": "success",
            "attack_path": attack_path
        }
    except Exception as e:
        print(f"生成替代攻击路径时出错: {e}")
        return {"status": "error", "details": str(e)}


def analyze_attack_failure(execution_result: Dict[str, Any], attack_path: Dict[str, Any]) -> Dict[str, Any]:
    """分析攻击失败的原因
    
    参数:
        execution_result: 攻击执行结果
        attack_path: 攻击路径信息
        
    返回:
        包含失败分析的字典
    """
    print("分析攻击失败原因...")
    try:
        client = get_llm_client()
        
        # 构建提示词
        prompt = f"""
你是一名专业的渗透测试专家，请分析以下攻击失败的原因：

### 攻击路径
```json
{json.dumps(attack_path, indent=2)}
```

### 执行结果
```json
{json.dumps(execution_result, indent=2)}
```

请分析失败的可能原因，并提供改进建议。考虑以下几个方面：
1. 代码执行错误
2. 目标系统防护措施
3. 漏洞利用条件不满足
4. 其他可能的技术问题

输出格式：
```json
{{
  "failure_reasons": [
    {{
      "reason": "失败原因描述",
      "probability": "高/中/低",
      "evidence": "从执行结果中提取的证据"
    }},
    // 更多原因...
  ],
  "improvement_suggestions": [
    {{
      "suggestion": "改进建议",
      "details": "详细说明"
    }},
    // 更多建议...
  ]
}}
```
"""
        
        # 调用大模型分析失败原因
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are a cybersecurity expert specialized in penetration testing and exploit development."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1
        )
        
        # 解析响应
        analysis_text = response.choices[0].message.content
        
        # 提取JSON部分
        try:
            # 尝试直接解析整个文本
            analysis = json.loads(analysis_text)
        except json.JSONDecodeError:
            # 如果失败，尝试提取```json和```之间的内容
            import re
            json_match = re.search(r'```json\n(.+?)\n```', analysis_text, re.DOTALL)
            if json_match:
                analysis = json.loads(json_match.group(1))
            else:
                # 如果仍然失败，返回原始文本
                analysis = {"raw_response": analysis_text}
        
        return {
            "status": "success",
            "failure_analysis": analysis
        }
    except Exception as e:
        print(f"分析攻击失败原因时出错: {e}")
        return {"status": "error", "details": str(e)}


# 测试函数
def test_generate_attack_path():
    """测试攻击路径生成功能"""
    # 模拟安全分析结果
    analysis_result = {
        "status": "success",
        "analysis": """
### 安全分析报告
#### 关键发现
- [高危] 发现开放的SSH服务(端口22)使用弱密码
  攻击向量: 可通过暴力破解获取SSH访问权限
  影响程度: 严重，可能导致系统完全被控制
  修复建议: 加强密码策略，启用SSH密钥认证

- [中危] Web服务器(端口80)运行过时的Apache 2.2.15版本
  攻击向量: 存在多个已知漏洞可被利用
  影响程度: 中等，可能导致信息泄露或服务中断
  修复建议: 升级到最新的Apache版本

#### 详细分析
主机********上发现以下开放端口和服务：
- 端口22 (SSH): OpenSSH 5.3
- 端口80 (HTTP): Apache 2.2.15
- 端口443 (HTTPS): Apache 2.2.15

#### 综合建议
立即加强SSH认证机制，升级Web服务器软件版本，并实施网络访问控制。"""
    }
    
    # 模拟目标信息
    target_info = {
        "ip": "********",
        "hostname": "webserver01",
        "os": "CentOS 6.5",
        "ports": [
            {"port": 22, "service": "SSH", "version": "OpenSSH 5.3"},
            {"port": 80, "service": "HTTP", "version": "Apache 2.2.15"},
            {"port": 443, "service": "HTTPS", "version": "Apache 2.2.15"}
        ],
        "web_info": {
            "domains": ["example.com"],
            "technologies": ["PHP 5.3.3", "MySQL 5.1.73"]
        }
    }
    
    # 生成攻击路径
    result = generate_attack_path(analysis_result, target_info)
    print(json.dumps(result, indent=2))


if __name__ == "__main__":
    # 如果直接运行此脚本，执行测试函数
    test_generate_attack_path()