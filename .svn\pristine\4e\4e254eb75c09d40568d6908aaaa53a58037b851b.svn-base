'''
    从数据库中获取域名，然后对域名进行全量爬虫，并进行保存
'''
#从domain_ip_mapping表中获取域名,提取其中第一条status为todo的数据，取出域名
# 然后对域名进行全量爬虫，分析全部的网页数据，然后提取url,网站内容进行保存，并将爬虫结果按照domain、url、result的形式保存到web_spider_results表中


import requests
from bs4 import BeautifulSoup
import pymysql
import configparser
import os
import time
import re  # 添加re模块导入
import urllib.parse
from concurrent.futures import ThreadPoolExecutor
from urllib3.exceptions import InsecureRequestWarning

# 禁用不安全请求的警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

def get_db_connection():
    """从配置文件中获取数据库连接"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
    config.read(config_path, encoding='utf-8')
    db_config = {
        'host': config['mysql']['host'],
        'port': int(config['mysql']['port']),
        'user': config['mysql']['user'],
        'password': config['mysql']['password'],
        'database': config['mysql']['database'],
        'connect_timeout': 60,
        'read_timeout': 300,
        'write_timeout': 300
    }
    return pymysql.connect(**db_config)

def get_todo_domain():
    """从domain_ip_mapping表中获取待爬取的域名"""
    try:
        db = get_db_connection()
        cursor = db.cursor()
        query = "SELECT domain FROM domain_ip_mapping WHERE status = 'todo' LIMIT 1"
        cursor.execute(query)
        domain = cursor.fetchone()
        cursor.close()
        db.close()
        return domain[0] if domain else None
    except Exception as e:
        print(f"获取待爬取域名时出错: {e}")
        return None

def save_spider_result(domain, url, content):
    """保存爬虫结果到数据库"""
    try:
        db = get_db_connection()
        cursor = db.cursor()
        query = """
        INSERT INTO web_spider_results (domain, url, url_content)
        VALUES (%s, %s, %s)
        ON DUPLICATE KEY UPDATE url_content = VALUES(url_content)
        """
        cursor.execute(query, (domain, url, content))
        db.commit()
        cursor.close()
        db.close()
    except Exception as e:
        print(f"保存爬虫结果时出错: {e}")
        if 'db' in locals() and db.open:
            db.rollback()
            db.close()

def detect_sensitive_info(content):
    """检测网页内容中的敏感信息"""
    sensitive_patterns = [
        r'\b\d{3}-\d{2}-\d{4}\b',  # 社会安全号码格式
        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # 电子邮件地址
        r'\b(?:api_key|secret_key|access_token|password)\b',  # 常见API和配置密钥
        r'\b(?:公司名称|地址|电话)\b'  # 示例公司信息关键词
    ]
    
    found_sensitive_info = []
    for pattern in sensitive_patterns:
        matches = re.findall(pattern, content)
        found_sensitive_info.extend(matches)
    
    return found_sensitive_info

def crawl_domain(domain):
    """爬取单个域名"""
    print(f"开始爬取域名: {domain}")
    visited_urls = set()
    to_visit = [f"http://{domain}", f"https://{domain}"]
    
    try:
        while to_visit:
            url = to_visit.pop(0)
            if url in visited_urls:
                continue
                
            visited_urls.add(url)
            
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
                response = requests.get(url, headers=headers, timeout=10, verify=False, allow_redirects=True)
                
                # 只处理HTML内容
                content_type = response.headers.get('Content-Type', '')
                if 'text/html' not in content_type:
                    continue
                
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 保存爬虫结果
                save_spider_result(domain, url, response.text)  # 全量保存爬虫结果
                
                # 检测敏感信息
                sensitive_info = detect_sensitive_info(response.text)
                if sensitive_info:
                    print(f"在 {url} 中发现敏感信息: {sensitive_info}")
                    #发现敏感信息就把信息存入数据库
                # 提取新的URL
                new_urls = extract_urls(soup, url)
                for new_url in new_urls:
                    # 只爬取同一域名下的URL
                    if domain in urllib.parse.urlparse(new_url).netloc and new_url not in visited_urls and new_url not in to_visit:
                        to_visit.append(new_url)
                
            except requests.exceptions.RequestException as e:
                print(f"请求 {url} 时出错: {e}")
            except Exception as e:
                print(f"处理 {url} 时出错: {e}")
            
            # 避免请求过于频繁
            time.sleep(1)
        
        print(f"完成爬取域名 {domain}，共爬取 {len(visited_urls)} 个URL")
        
    except Exception as e:
        print(f"爬取域名 {domain} 时出错: {e}")

def extract_urls(soup, base_url):
    """从页面中提取URL"""
    urls = []
    for a_tag in soup.find_all('a', href=True):
        href = a_tag['href']
        if href and not href.startswith(('#', 'javascript:', 'mailto:', 'tel:')):
            full_url = normalize_url(base_url, href)
            if is_valid_url(full_url):
                urls.append(full_url)
    return urls

def normalize_url(base_url, url):
    """标准化URL"""
    # 处理相对路径
    if not url.startswith('http'):
        return urllib.parse.urljoin(base_url, url)
    return url

def is_valid_url(url):
    """检查URL是否有效"""
    # 排除常见的非网页资源
    excluded_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.css', '.js', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar']
    for ext in excluded_extensions:
        if url.lower().endswith(ext):
            return False
    return True

def main():
    """主函数"""
    # 使用线程池并发爬取
    with ThreadPoolExecutor(max_workers=3) as executor:
        while True:
            domain = get_todo_domain()
            if not domain:
                print("没有新的域名需要爬取,退出...")
                exit(0)
            
            executor.submit(crawl_domain, domain)
            
            # 等待一段时间再获取新的域名
            time.sleep(60)


main()

