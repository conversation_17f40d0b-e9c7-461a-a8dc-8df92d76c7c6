"""
存储集成模块

功能：
1. 将存储管理器集成到现有的信息收集系统
2. 提供统一的存储接口
3. 自动处理数据转换和映射
4. 确保数据完整性和一致性

作者：AI渗透测试系统
版本：1.0.0
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from info_collector.database_storage.storage_manager import DatabaseStorageManager

logger = logging.getLogger(__name__)


class StorageIntegrator:
    """存储集成器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化存储集成器
        
        Args:
            config_path: 配置文件路径
        """
        self.storage_manager = DatabaseStorageManager(config_path)
        logger.info("存储集成器初始化完成")
    
    def store_comprehensive_collector_results(self, results: Dict[str, Any]) -> bool:
        """
        存储comprehensive_collector的扫描结果
        
        Args:
            results: comprehensive_collector的扫描结果
            
        Returns:
            bool: 存储是否成功
        """
        try:
            target = results.get('target', '')
            if not target:
                logger.error("扫描结果中缺少目标信息")
                return False
            
            # 转换为标准格式
            standardized_results = self._convert_comprehensive_results(results)
            
            # 存储到数据库
            success = self.storage_manager.store_comprehensive_scan_results(standardized_results)
            
            if success:
                logger.info(f"成功存储comprehensive_collector结果：{target}")
            else:
                logger.error(f"存储comprehensive_collector结果失败：{target}")
            
            return success
            
        except Exception as e:
            logger.error(f"存储comprehensive_collector结果异常：{e}")
            return False
    
    def store_awvs_results(self, domain: str, awvs_results: Dict[str, Any]) -> bool:
        """
        存储AWVS扫描结果
        
        Args:
            domain: 域名
            awvs_results: AWVS扫描结果
            
        Returns:
            bool: 存储是否成功
        """
        try:
            # 转换为标准格式
            standardized_results = self._convert_awvs_results(domain, awvs_results)
            
            # 存储到数据库
            success = self.storage_manager.store_awvs_scan_results(domain, standardized_results)
            
            if success:
                logger.info(f"成功存储AWVS结果：{domain}")
            else:
                logger.error(f"存储AWVS结果失败：{domain}")
            
            return success
            
        except Exception as e:
            logger.error(f"存储AWVS结果异常：{e}")
            return False
    
    def store_database_scan_results(self, target: str, db_results: Dict[str, Any]) -> bool:
        """
        存储数据库扫描结果
        
        Args:
            target: 目标
            db_results: 数据库扫描结果
            
        Returns:
            bool: 存储是否成功
        """
        try:
            # 转换为标准格式
            standardized_results = self._convert_database_results(target, db_results)
            
            # 存储到数据库
            success = self.storage_manager.store_comprehensive_scan_results(standardized_results)
            
            if success:
                logger.info(f"成功存储数据库扫描结果：{target}")
            else:
                logger.error(f"存储数据库扫描结果失败：{target}")
            
            return success
            
        except Exception as e:
            logger.error(f"存储数据库扫描结果异常：{e}")
            return False
    
    def _convert_comprehensive_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换comprehensive_collector结果为标准格式
        
        Args:
            results: 原始结果
            
        Returns:
            Dict[str, Any]: 标准格式结果
        """
        target = results.get('target', '')
        
        standardized = {
            'target': target,
            'scan_time': results.get('scan_time', datetime.now().isoformat()),
            'scan_duration': results.get('scan_duration', 0),
            'scanner_version': results.get('version', '1.0.0'),
            'confidence_score': 1.0
        }
        
        # 转换网络信息
        if results.get('network_scan'):
            network_info = results['network_scan']
            standardized['network_info'] = {
                'hostname': network_info.get('hostname'),
                'os_type': network_info.get('os_name'),
                'os_version': network_info.get('os_version'),
                'os_accuracy': network_info.get('os_accuracy', 0),
                'response_time': network_info.get('response_time'),
                'ttl': network_info.get('ttl'),
                'scan_method': 'nmap'
            }
        
        # 转换服务信息
        services = []
        if results.get('port_scan'):
            for port_info in results['port_scan'].get('open_ports', []):
                service = {
                    'port': port_info.get('port'),
                    'protocol': port_info.get('protocol', 'tcp'),
                    'service_name': port_info.get('service'),
                    'service_version': port_info.get('version'),
                    'service_product': port_info.get('product'),
                    'service_banner': port_info.get('banner'),
                    'state': 'open',
                    'confidence_score': port_info.get('confidence', 0) / 10.0
                }
                services.append(service)
        
        standardized['services'] = services
        
        # 转换Web应用信息
        web_apps = []
        if results.get('web_scan'):
            for url, web_info in results['web_scan'].items():
                web_app = {
                    'url': url,
                    'title': web_info.get('title'),
                    'server': web_info.get('server'),
                    'powered_by': web_info.get('x_powered_by'),
                    'cms_type': web_info.get('cms'),
                    'response_code': web_info.get('status_code'),
                    'content_length': web_info.get('content_length'),
                    'content_type': web_info.get('content_type'),
                    'technologies': web_info.get('technologies', [])
                }
                web_apps.append(web_app)
        
        standardized['web_apps'] = web_apps
        
        # 转换漏洞信息
        vulnerabilities = []
        if results.get('vulnerabilities'):
            for vuln in results['vulnerabilities']:
                vulnerability = {
                    'vuln_type': vuln.get('type', 'unknown'),
                    'vuln_name': vuln.get('name'),
                    'severity': vuln.get('severity', 'unknown'),
                    'cvss_score': vuln.get('cvss_score', 0.0),
                    'cve_id': vuln.get('cve'),
                    'description': vuln.get('description'),
                    'solution': vuln.get('solution'),
                    'verified': vuln.get('verified', False)
                }
                vulnerabilities.append(vulnerability)
        
        standardized['vulnerabilities'] = vulnerabilities
        
        return standardized
    
    def _convert_awvs_results(self, domain: str, awvs_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换AWVS结果为标准格式
        
        Args:
            domain: 域名
            awvs_results: AWVS结果
            
        Returns:
            Dict[str, Any]: 标准格式结果
        """
        standardized = {
            'domain': domain,
            'scan_type': 'awvs',
            'status': awvs_results.get('status'),
            'target_id': awvs_results.get('target_id'),
            'scan_id': awvs_results.get('scan_id'),
            'results': awvs_results.get('results', {})
        }
        
        # 转换漏洞信息
        if awvs_results.get('results') and awvs_results['results'].get('vulnerabilities'):
            vulnerabilities = []
            for vuln in awvs_results['results']['vulnerabilities']:
                vulnerability = {
                    'vuln_name': vuln.get('vuln_name', 'Web Vulnerability'),
                    'severity': self._map_awvs_severity(vuln.get('severity')),
                    'cvss_score': vuln.get('cvss_score', 0.0),
                    'description': vuln.get('description'),
                    'recommendation': vuln.get('recommendation'),
                    'affects': vuln.get('affects'),
                    'request': vuln.get('request'),
                    'response': vuln.get('response')
                }
                vulnerabilities.append(vulnerability)
            
            standardized['results']['vulnerabilities'] = vulnerabilities
        
        return standardized
    
    def _convert_database_results(self, target: str, db_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换数据库扫描结果为标准格式
        
        Args:
            target: 目标
            db_results: 数据库扫描结果
            
        Returns:
            Dict[str, Any]: 标准格式结果
        """
        standardized = {
            'target': target,
            'scan_time': db_results.get('scan_time', datetime.now().isoformat()),
            'scan_duration': db_results.get('scan_duration', 0),
            'scanner_version': '1.0.0',
            'confidence_score': 1.0
        }
        
        # 转换数据库信息
        databases = []
        if db_results.get('discovered_databases'):
            for db in db_results['discovered_databases']:
                database = {
                    'type': db.get('type'),
                    'host': db.get('host'),
                    'port': db.get('port'),
                    'version': db.get('version'),
                    'authentication': db.get('authentication', {}),
                    'databases': db.get('databases', []),
                    'users': db.get('users', []),
                    'configuration': db.get('configuration', {}),
                    'ssl_enabled': db.get('ssl_enabled', False),
                    'vulnerabilities': db.get('vulnerabilities', [])
                }
                databases.append(database)
        
        standardized['databases'] = databases
        
        # 转换漏洞信息
        vulnerabilities = []
        if db_results.get('vulnerabilities'):
            for vuln in db_results['vulnerabilities']:
                vulnerability = {
                    'vuln_type': 'database',
                    'vuln_name': vuln.get('description', 'Database Vulnerability'),
                    'severity': vuln.get('severity', 'unknown'),
                    'cve_id': vuln.get('cve'),
                    'description': vuln.get('description'),
                    'solution': vuln.get('recommendation'),
                    'vuln_data': vuln
                }
                vulnerabilities.append(vulnerability)
        
        standardized['vulnerabilities'] = vulnerabilities
        
        return standardized
    
    def _map_awvs_severity(self, awvs_severity: str) -> str:
        """
        映射AWVS严重程度到标准严重程度
        
        Args:
            awvs_severity: AWVS严重程度
            
        Returns:
            str: 标准严重程度
        """
        severity_mapping = {
            'critical': 'critical',
            'high': 'high',
            'medium': 'medium',
            'low': 'low',
            'info': 'info',
            'informational': 'info'
        }
        
        return severity_mapping.get(awvs_severity.lower() if awvs_severity else '', 'unknown')
    
    def get_storage_statistics(self) -> Dict[str, Any]:
        """
        获取存储统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.storage_manager.get_asset_statistics()
    
    def cleanup_old_data(self, days_to_keep: int = 30) -> bool:
        """
        清理过期数据
        
        Args:
            days_to_keep: 保留天数
            
        Returns:
            bool: 清理是否成功
        """
        try:
            with self.storage_manager.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.callproc('CleanupOldData', [days_to_keep])
                conn.commit()
                cursor.close()
                
                logger.info(f"成功清理{days_to_keep}天前的过期数据")
                return True
                
        except Exception as e:
            logger.error(f"清理过期数据失败：{e}")
            return False


# 全局存储集成器实例
_storage_integrator = None

def get_storage_integrator(config_path: Optional[str] = None) -> StorageIntegrator:
    """
    获取全局存储集成器实例
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        StorageIntegrator: 存储集成器实例
    """
    global _storage_integrator
    if _storage_integrator is None:
        _storage_integrator = StorageIntegrator(config_path)
    return _storage_integrator


# 便捷函数
def store_scan_results(results: Dict[str, Any], scan_type: str = 'comprehensive') -> bool:
    """
    存储扫描结果的便捷函数
    
    Args:
        results: 扫描结果
        scan_type: 扫描类型
        
    Returns:
        bool: 存储是否成功
    """
    integrator = get_storage_integrator()
    
    if scan_type == 'comprehensive':
        return integrator.store_comprehensive_collector_results(results)
    elif scan_type == 'awvs':
        domain = results.get('domain', '')
        return integrator.store_awvs_results(domain, results)
    elif scan_type == 'database':
        target = results.get('target', '')
        return integrator.store_database_scan_results(target, results)
    else:
        logger.error(f"不支持的扫描类型：{scan_type}")
        return False


# 使用示例
if __name__ == "__main__":
    # 创建存储集成器
    integrator = StorageIntegrator()
    
    # 示例：存储comprehensive_collector结果
    sample_results = {
        'target': '*************',
        'scan_time': datetime.now().isoformat(),
        'network_scan': {
            'hostname': 'test-server',
            'os_name': 'Linux',
            'response_time': 10
        },
        'port_scan': {
            'open_ports': [
                {
                    'port': 80,
                    'protocol': 'tcp',
                    'service': 'http',
                    'version': 'Apache 2.4.41'
                }
            ]
        }
    }
    
    # 存储结果
    success = integrator.store_comprehensive_collector_results(sample_results)
    print(f"存储结果：{'成功' if success else '失败'}")
    
    # 获取统计信息
    stats = integrator.get_storage_statistics()
    print(f"存储统计：{stats}")
