#使用api连接deepseek
# 先安装SDK：pip3 install openai（网页2/8）
from openai import OpenAI

client = OpenAI(
    api_key="sk-24f69fa80ce44b148c668dd931429720",  # 网页2/8
    base_url="https://api.deepseek.com"  # 网页8
)

response = client.chat.completions.create(
    model="deepseek-chat",  # 网页8
    messages=[
        {"role": "user", "content": "请用DeepSeek模型生成一首中文短诗"}
    ],
    temperature=0.5,
    max_tokens=200,
    stream=True  # 网页8支持流式响应
)

# 流式输出处理（网页8）
for chunk in response:
    print(chunk.choices[0].delta.content, end="", flush=True)