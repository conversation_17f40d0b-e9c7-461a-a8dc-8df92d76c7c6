"""
威胁情报收集器

缺失功能：
1. 多源威胁情报聚合 - VirusTotal、Shodan、Censys、AlienVault
2. IP/域名信誉检查 - 恶意IP、域名黑名单
3. 恶意软件分析 - 文件哈希、行为分析
4. 攻击模式识别 - IOC提取、TTP分析
5. 地理位置分析 - IP地理位置、ASN信息
6. 历史攻击记录 - 攻击时间线、频率分析
7. 威胁评分系统 - 综合威胁评分

作者：AI渗透测试系统
版本：1.0.0
"""

import requests
import json
import time
import hashlib
import base64
from typing import Dict, List, Any, Optional
import logging
import ipaddress
import socket
import geoip2.database
import geoip2.errors
from urllib.parse import urlparse
import re
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ThreatIntelligenceCollector:
    """威胁情报收集器"""
    
    def __init__(self, config: Dict[str, str] = None):
        """
        初始化威胁情报收集器
        
        Args:
            config: API配置字典，包含各种服务的API密钥
        """
        self.config = config or {}
        
        # API配置
        self.virustotal_api_key = self.config.get('virustotal_api_key', '')
        self.shodan_api_key = self.config.get('shodan_api_key', '')
        self.censys_api_id = self.config.get('censys_api_id', '')
        self.censys_api_secret = self.config.get('censys_api_secret', '')
        self.abuseipdb_api_key = self.config.get('abuseipdb_api_key', '')
        
        # API端点
        self.api_endpoints = {
            'virustotal_ip': 'https://www.virustotal.com/vtapi/v2/ip-address/report',
            'virustotal_domain': 'https://www.virustotal.com/vtapi/v2/domain/report',
            'virustotal_url': 'https://www.virustotal.com/vtapi/v2/url/report',
            'shodan_host': 'https://api.shodan.io/shodan/host/',
            'shodan_search': 'https://api.shodan.io/shodan/host/search',
            'censys_ipv4': 'https://search.censys.io/api/v2/hosts/',
            'abuseipdb': 'https://api.abuseipdb.com/api/v2/check'
        }
        
        # 威胁分类
        self.threat_categories = {
            'malware': ['trojan', 'virus', 'worm', 'backdoor', 'rootkit'],
            'phishing': ['phishing', 'scam', 'fraud'],
            'botnet': ['botnet', 'c2', 'command_control'],
            'scanner': ['scanner', 'probe', 'reconnaissance'],
            'spam': ['spam', 'email_abuse'],
            'exploit': ['exploit', 'vulnerability', 'attack']
        }
    
    def collect_threat_intelligence(self, target: str, target_type: str = 'auto') -> Dict[str, Any]:
        """
        收集威胁情报
        
        Args:
            target: 目标（IP、域名、URL、文件哈希）
            target_type: 目标类型 ('ip', 'domain', 'url', 'hash', 'auto')
            
        Returns:
            Dict: 威胁情报结果
        """
        logger.info(f"开始收集威胁情报：{target}")
        
        # 自动检测目标类型
        if target_type == 'auto':
            target_type = self._detect_target_type(target)
        
        threat_intel = {
            'target': target,
            'target_type': target_type,
            'scan_time': datetime.now().isoformat(),
            'sources': {},
            'reputation': {
                'malicious': False,
                'suspicious': False,
                'clean': False,
                'score': 0,
                'confidence': 0
            },
            'geolocation': {},
            'network_info': {},
            'threat_indicators': [],
            'historical_data': {},
            'recommendations': []
        }
        
        try:
            # 根据目标类型收集不同的情报
            if target_type == 'ip':
                threat_intel.update(self._collect_ip_intelligence(target))
            elif target_type == 'domain':
                threat_intel.update(self._collect_domain_intelligence(target))
            elif target_type == 'url':
                threat_intel.update(self._collect_url_intelligence(target))
            elif target_type == 'hash':
                threat_intel.update(self._collect_hash_intelligence(target))
            
            # 综合分析和评分
            threat_intel['reputation'] = self._calculate_reputation_score(threat_intel)
            threat_intel['recommendations'] = self._generate_recommendations(threat_intel)
            
        except Exception as e:
            logger.error(f"威胁情报收集失败：{e}")
            threat_intel['error'] = str(e)
        
        return threat_intel
    
    def _detect_target_type(self, target: str) -> str:
        """自动检测目标类型"""
        # IP地址检测
        try:
            ipaddress.ip_address(target)
            return 'ip'
        except ValueError:
            pass
        
        # URL检测
        if target.startswith(('http://', 'https://')):
            return 'url'
        
        # 文件哈希检测
        if re.match(r'^[a-fA-F0-9]{32}$', target):  # MD5
            return 'hash'
        elif re.match(r'^[a-fA-F0-9]{40}$', target):  # SHA1
            return 'hash'
        elif re.match(r'^[a-fA-F0-9]{64}$', target):  # SHA256
            return 'hash'
        
        # 默认为域名
        return 'domain'
    
    def _collect_ip_intelligence(self, ip: str) -> Dict[str, Any]:
        """收集IP威胁情报"""
        ip_intel = {
            'sources': {},
            'geolocation': {},
            'network_info': {},
            'threat_indicators': []
        }
        
        # VirusTotal IP查询
        if self.virustotal_api_key:
            ip_intel['sources']['virustotal'] = self._query_virustotal_ip(ip)
        
        # Shodan查询
        if self.shodan_api_key:
            ip_intel['sources']['shodan'] = self._query_shodan_ip(ip)
        
        # Censys查询
        if self.censys_api_id and self.censys_api_secret:
            ip_intel['sources']['censys'] = self._query_censys_ip(ip)
        
        # AbuseIPDB查询
        if self.abuseipdb_api_key:
            ip_intel['sources']['abuseipdb'] = self._query_abuseipdb(ip)
        
        # 地理位置信息
        ip_intel['geolocation'] = self._get_ip_geolocation(ip)
        
        # 网络信息
        ip_intel['network_info'] = self._get_network_info(ip)
        
        return ip_intel
    
    def _collect_domain_intelligence(self, domain: str) -> Dict[str, Any]:
        """收集域名威胁情报"""
        domain_intel = {
            'sources': {},
            'dns_info': {},
            'whois_info': {},
            'threat_indicators': []
        }
        
        # VirusTotal域名查询
        if self.virustotal_api_key:
            domain_intel['sources']['virustotal'] = self._query_virustotal_domain(domain)
        
        # DNS信息
        domain_intel['dns_info'] = self._get_domain_dns_info(domain)
        
        # Whois信息
        domain_intel['whois_info'] = self._get_domain_whois_info(domain)
        
        return domain_intel
    
    def _collect_url_intelligence(self, url: str) -> Dict[str, Any]:
        """收集URL威胁情报"""
        url_intel = {
            'sources': {},
            'url_analysis': {},
            'threat_indicators': []
        }
        
        # VirusTotal URL查询
        if self.virustotal_api_key:
            url_intel['sources']['virustotal'] = self._query_virustotal_url(url)
        
        # URL分析
        url_intel['url_analysis'] = self._analyze_url_structure(url)
        
        return url_intel
    
    def _collect_hash_intelligence(self, file_hash: str) -> Dict[str, Any]:
        """收集文件哈希威胁情报"""
        hash_intel = {
            'sources': {},
            'file_analysis': {},
            'threat_indicators': []
        }
        
        # VirusTotal文件查询
        if self.virustotal_api_key:
            hash_intel['sources']['virustotal'] = self._query_virustotal_hash(file_hash)
        
        return hash_intel
    
    def _query_virustotal_ip(self, ip: str) -> Dict[str, Any]:
        """查询VirusTotal IP信息"""
        try:
            params = {
                'apikey': self.virustotal_api_key,
                'ip': ip
            }
            
            response = requests.get(self.api_endpoints['virustotal_ip'], params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'response_code': data.get('response_code'),
                    'detected_urls': data.get('detected_urls', []),
                    'detected_downloaded_samples': data.get('detected_downloaded_samples', []),
                    'detected_communicating_samples': data.get('detected_communicating_samples', []),
                    'resolutions': data.get('resolutions', []),
                    'country': data.get('country'),
                    'as_owner': data.get('as_owner'),
                    'asn': data.get('asn')
                }
            else:
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            logger.error(f"VirusTotal IP查询失败：{e}")
            return {'error': str(e)}
    
    def _query_virustotal_domain(self, domain: str) -> Dict[str, Any]:
        """查询VirusTotal域名信息"""
        try:
            params = {
                'apikey': self.virustotal_api_key,
                'domain': domain
            }
            
            response = requests.get(self.api_endpoints['virustotal_domain'], params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'response_code': data.get('response_code'),
                    'detected_urls': data.get('detected_urls', []),
                    'detected_downloaded_samples': data.get('detected_downloaded_samples', []),
                    'detected_communicating_samples': data.get('detected_communicating_samples', []),
                    'resolutions': data.get('resolutions', []),
                    'subdomains': data.get('subdomains', []),
                    'categories': data.get('categories', []),
                    'whois': data.get('whois')
                }
            else:
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            logger.error(f"VirusTotal域名查询失败：{e}")
            return {'error': str(e)}
    
    def _query_virustotal_url(self, url: str) -> Dict[str, Any]:
        """查询VirusTotal URL信息"""
        try:
            params = {
                'apikey': self.virustotal_api_key,
                'resource': url
            }
            
            response = requests.get(self.api_endpoints['virustotal_url'], params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'response_code': data.get('response_code'),
                    'scan_date': data.get('scan_date'),
                    'positives': data.get('positives', 0),
                    'total': data.get('total', 0),
                    'scans': data.get('scans', {}),
                    'permalink': data.get('permalink')
                }
            else:
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            logger.error(f"VirusTotal URL查询失败：{e}")
            return {'error': str(e)}
    
    def _query_virustotal_hash(self, file_hash: str) -> Dict[str, Any]:
        """查询VirusTotal文件哈希信息"""
        try:
            params = {
                'apikey': self.virustotal_api_key,
                'resource': file_hash
            }
            
            response = requests.get('https://www.virustotal.com/vtapi/v2/file/report', params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'response_code': data.get('response_code'),
                    'scan_date': data.get('scan_date'),
                    'positives': data.get('positives', 0),
                    'total': data.get('total', 0),
                    'scans': data.get('scans', {}),
                    'md5': data.get('md5'),
                    'sha1': data.get('sha1'),
                    'sha256': data.get('sha256'),
                    'permalink': data.get('permalink')
                }
            else:
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            logger.error(f"VirusTotal文件查询失败：{e}")
            return {'error': str(e)}
    
    def _query_shodan_ip(self, ip: str) -> Dict[str, Any]:
        """查询Shodan IP信息"""
        try:
            url = f"{self.api_endpoints['shodan_host']}{ip}"
            params = {'key': self.shodan_api_key}
            
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'ip': data.get('ip_str'),
                    'hostnames': data.get('hostnames', []),
                    'country_name': data.get('country_name'),
                    'city': data.get('city'),
                    'region_code': data.get('region_code'),
                    'postal_code': data.get('postal_code'),
                    'latitude': data.get('latitude'),
                    'longitude': data.get('longitude'),
                    'isp': data.get('isp'),
                    'org': data.get('org'),
                    'asn': data.get('asn'),
                    'ports': data.get('ports', []),
                    'vulns': data.get('vulns', []),
                    'tags': data.get('tags', []),
                    'last_update': data.get('last_update')
                }
            else:
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            logger.error(f"Shodan查询失败：{e}")
            return {'error': str(e)}
    
    def _query_censys_ip(self, ip: str) -> Dict[str, Any]:
        """查询Censys IP信息"""
        try:
            url = f"{self.api_endpoints['censys_ipv4']}{ip}"
            auth = (self.censys_api_id, self.censys_api_secret)
            
            response = requests.get(url, auth=auth, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'ip': data.get('ip'),
                    'services': data.get('services', []),
                    'location': data.get('location', {}),
                    'autonomous_system': data.get('autonomous_system', {}),
                    'last_updated_at': data.get('last_updated_at')
                }
            else:
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            logger.error(f"Censys查询失败：{e}")
            return {'error': str(e)}
    
    def _query_abuseipdb(self, ip: str) -> Dict[str, Any]:
        """查询AbuseIPDB信息"""
        try:
            headers = {
                'Key': self.abuseipdb_api_key,
                'Accept': 'application/json'
            }
            
            params = {
                'ipAddress': ip,
                'maxAgeInDays': 90,
                'verbose': ''
            }
            
            response = requests.get(self.api_endpoints['abuseipdb'], headers=headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('data', {})
            else:
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            logger.error(f"AbuseIPDB查询失败：{e}")
            return {'error': str(e)}
    
    def _get_ip_geolocation(self, ip: str) -> Dict[str, Any]:
        """获取IP地理位置信息"""
        geolocation = {
            'country': None,
            'region': None,
            'city': None,
            'latitude': None,
            'longitude': None,
            'timezone': None,
            'isp': None,
            'organization': None
        }
        
        try:
            # 使用免费的IP地理位置API
            response = requests.get(f"http://ip-api.com/json/{ip}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    geolocation.update({
                        'country': data.get('country'),
                        'region': data.get('regionName'),
                        'city': data.get('city'),
                        'latitude': data.get('lat'),
                        'longitude': data.get('lon'),
                        'timezone': data.get('timezone'),
                        'isp': data.get('isp'),
                        'organization': data.get('org')
                    })
        
        except Exception as e:
            logger.debug(f"地理位置查询失败：{e}")
        
        return geolocation
    
    def _get_network_info(self, ip: str) -> Dict[str, Any]:
        """获取网络信息"""
        network_info = {
            'asn': None,
            'as_name': None,
            'network_range': None,
            'reverse_dns': None
        }
        
        try:
            # 反向DNS查询
            try:
                reverse_dns = socket.gethostbyaddr(ip)[0]
                network_info['reverse_dns'] = reverse_dns
            except:
                pass
            
            # 使用免费API获取ASN信息
            response = requests.get(f"https://ipapi.co/{ip}/json/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                network_info.update({
                    'asn': data.get('asn'),
                    'as_name': data.get('org'),
                    'network_range': data.get('network')
                })
        
        except Exception as e:
            logger.debug(f"网络信息查询失败：{e}")
        
        return network_info
    
    def _get_domain_dns_info(self, domain: str) -> Dict[str, Any]:
        """获取域名DNS信息"""
        dns_info = {
            'a_records': [],
            'mx_records': [],
            'ns_records': [],
            'txt_records': []
        }
        
        try:
            import dns.resolver
            
            # A记录
            try:
                answers = dns.resolver.resolve(domain, 'A')
                dns_info['a_records'] = [str(answer) for answer in answers]
            except:
                pass
            
            # MX记录
            try:
                answers = dns.resolver.resolve(domain, 'MX')
                dns_info['mx_records'] = [f"{answer.preference} {answer.exchange}" for answer in answers]
            except:
                pass
            
            # NS记录
            try:
                answers = dns.resolver.resolve(domain, 'NS')
                dns_info['ns_records'] = [str(answer) for answer in answers]
            except:
                pass
            
            # TXT记录
            try:
                answers = dns.resolver.resolve(domain, 'TXT')
                dns_info['txt_records'] = [str(answer) for answer in answers]
            except:
                pass
        
        except Exception as e:
            logger.debug(f"DNS信息查询失败：{e}")
        
        return dns_info
    
    def _get_domain_whois_info(self, domain: str) -> Dict[str, Any]:
        """获取域名Whois信息"""
        whois_info = {}
        
        try:
            import whois
            w = whois.whois(domain)
            whois_info = {
                'registrar': w.registrar,
                'creation_date': str(w.creation_date) if w.creation_date else None,
                'expiration_date': str(w.expiration_date) if w.expiration_date else None,
                'updated_date': str(w.updated_date) if w.updated_date else None,
                'status': w.status,
                'nameservers': w.name_servers
            }
        except Exception as e:
            logger.debug(f"Whois查询失败：{e}")
            whois_info['error'] = str(e)
        
        return whois_info
    
    def _analyze_url_structure(self, url: str) -> Dict[str, Any]:
        """分析URL结构"""
        analysis = {
            'scheme': None,
            'domain': None,
            'path': None,
            'query': None,
            'fragment': None,
            'suspicious_patterns': []
        }
        
        try:
            parsed = urlparse(url)
            analysis.update({
                'scheme': parsed.scheme,
                'domain': parsed.netloc,
                'path': parsed.path,
                'query': parsed.query,
                'fragment': parsed.fragment
            })
            
            # 检测可疑模式
            suspicious_patterns = [
                r'[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}',  # IP地址
                r'[a-zA-Z0-9]{20,}',  # 长随机字符串
                r'\.tk$|\.ml$|\.ga$|\.cf$',  # 免费域名
                r'bit\.ly|tinyurl|t\.co',  # 短链接
            ]
            
            for pattern in suspicious_patterns:
                if re.search(pattern, url):
                    analysis['suspicious_patterns'].append(pattern)
        
        except Exception as e:
            logger.debug(f"URL分析失败：{e}")
            analysis['error'] = str(e)
        
        return analysis
    
    def _calculate_reputation_score(self, threat_intel: Dict[str, Any]) -> Dict[str, Any]:
        """计算信誉评分"""
        reputation = {
            'malicious': False,
            'suspicious': False,
            'clean': False,
            'score': 0,
            'confidence': 0,
            'reasons': []
        }
        
        try:
            total_score = 0
            total_weight = 0
            
            # VirusTotal评分
            vt_data = threat_intel.get('sources', {}).get('virustotal', {})
            if 'positives' in vt_data and 'total' in vt_data:
                vt_score = (vt_data['positives'] / vt_data['total']) * 100
                total_score += vt_score * 0.4  # 40%权重
                total_weight += 0.4
                
                if vt_data['positives'] > 0:
                    reputation['reasons'].append(f"VirusTotal检测到{vt_data['positives']}个威胁")
            
            # AbuseIPDB评分
            abuse_data = threat_intel.get('sources', {}).get('abuseipdb', {})
            if 'abuseConfidencePercentage' in abuse_data:
                abuse_score = abuse_data['abuseConfidencePercentage']
                total_score += abuse_score * 0.3  # 30%权重
                total_weight += 0.3
                
                if abuse_score > 25:
                    reputation['reasons'].append(f"AbuseIPDB信心度：{abuse_score}%")
            
            # Shodan标签评分
            shodan_data = threat_intel.get('sources', {}).get('shodan', {})
            if 'tags' in shodan_data:
                malicious_tags = ['malware', 'botnet', 'honeypot', 'scanner']
                found_tags = [tag for tag in shodan_data['tags'] if tag in malicious_tags]
                if found_tags:
                    total_score += 50 * 0.2  # 20%权重
                    total_weight += 0.2
                    reputation['reasons'].append(f"Shodan恶意标签：{found_tags}")
            
            # 计算最终评分
            if total_weight > 0:
                reputation['score'] = total_score / total_weight
                reputation['confidence'] = min(total_weight * 100, 100)
            
            # 分类
            if reputation['score'] >= 70:
                reputation['malicious'] = True
            elif reputation['score'] >= 30:
                reputation['suspicious'] = True
            else:
                reputation['clean'] = True
        
        except Exception as e:
            logger.debug(f"信誉评分计算失败：{e}")
        
        return reputation
    
    def _generate_recommendations(self, threat_intel: Dict[str, Any]) -> List[str]:
        """生成安全建议"""
        recommendations = []
        
        reputation = threat_intel.get('reputation', {})
        
        if reputation.get('malicious'):
            recommendations.extend([
                "立即阻止与此目标的所有通信",
                "检查系统是否已被感染",
                "更新安全策略和防火墙规则",
                "监控相关网络流量"
            ])
        elif reputation.get('suspicious'):
            recommendations.extend([
                "增强对此目标的监控",
                "限制与此目标的通信",
                "定期重新评估威胁等级",
                "收集更多威胁情报"
            ])
        else:
            recommendations.append("目前未发现明显威胁，建议定期监控")
        
        return recommendations


# 使用示例
if __name__ == "__main__":
    # 配置API密钥
    config = {
        'virustotal_api_key': 'your_virustotal_api_key',
        'shodan_api_key': 'your_shodan_api_key',
        'abuseipdb_api_key': 'your_abuseipdb_api_key'
    }
    
    collector = ThreatIntelligenceCollector(config)
    
    # 测试IP威胁情报收集
    result = collector.collect_threat_intelligence("*******", "ip")
    print(json.dumps(result, indent=2, ensure_ascii=False))
