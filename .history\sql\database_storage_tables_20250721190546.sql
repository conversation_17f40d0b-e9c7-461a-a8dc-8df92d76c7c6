-- 数据库存储表结构
-- 确保所有信息收集模块的数据都能正确存储

-- 1. 目标资产表（已存在，确保字段完整）
CREATE TABLE IF NOT EXISTS target_assets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_type ENUM('ip', 'domain', 'url', 'service', 'network') NOT NULL,
    asset_value VARCHAR(2048) NOT NULL,
    asset_name VARCHAR(255),
    description TEXT,
    business_importance ENUM('critical', 'high', 'medium', 'low') DEFAULT 'medium',
    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_scan_time TIMESTAMP NULL,
    status ENUM('active', 'inactive', 'unknown', 'archived') DEFAULT 'unknown',
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_asset_type_value (asset_type, asset_value(255)),
    INDEX idx_last_scan (last_scan_time),
    INDEX idx_status (status),
    UNIQUE KEY unique_asset (asset_type, asset_value(255))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 网络信息表（已存在，确保字段完整）
CREATE TABLE IF NOT EXISTS network_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    ip_address VARCHAR(45) NOT NULL,
    mac_address VARCHAR(17),
    hostname VARCHAR(255),
    os_type VARCHAR(100),
    os_version VARCHAR(255),
    os_accuracy INT DEFAULT 0,
    network_segment VARCHAR(45),
    gateway VARCHAR(45),
    response_time INT,
    ttl INT,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scan_method VARCHAR(50),
    scan_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ip (ip_address),
    INDEX idx_asset_id (asset_id),
    INDEX idx_network_segment (network_segment),
    INDEX idx_last_seen (last_seen),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 服务信息表（已存在，确保字段完整）
CREATE TABLE IF NOT EXISTS service_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    ip_address VARCHAR(45) NOT NULL,
    port INT NOT NULL,
    protocol ENUM('tcp', 'udp', 'sctp') NOT NULL,
    service_name VARCHAR(100),
    service_version VARCHAR(255),
    service_product VARCHAR(255),
    service_banner TEXT,
    service_extrainfo TEXT,
    state ENUM('open', 'closed', 'filtered', 'unfiltered', 'open|filtered', 'closed|filtered') DEFAULT 'open',
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_verified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    service_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ip_port (ip_address, port),
    INDEX idx_asset_id (asset_id),
    INDEX idx_service_name (service_name),
    INDEX idx_port (port),
    INDEX idx_state (state),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Web应用信息表（已存在，确保字段完整）
CREATE TABLE IF NOT EXISTS web_app_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    url VARCHAR(2048) NOT NULL,
    domain VARCHAR(255),
    ip_address VARCHAR(45),
    port INT DEFAULT 80,
    protocol ENUM('http', 'https') DEFAULT 'http',
    title VARCHAR(500),
    description TEXT,
    keywords TEXT,
    server VARCHAR(255),
    powered_by VARCHAR(255),
    cms_type VARCHAR(100),
    cms_version VARCHAR(100),
    technologies JSON,
    response_code INT,
    content_length BIGINT,
    content_type VARCHAR(255),
    charset VARCHAR(50),
    language VARCHAR(50),
    last_modified TIMESTAMP NULL,
    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_crawled TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    web_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_url (url(255)),
    INDEX idx_domain (domain),
    INDEX idx_asset_id (asset_id),
    INDEX idx_cms_type (cms_type),
    INDEX idx_response_code (response_code),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. 漏洞信息表（已存在，确保字段完整）
CREATE TABLE IF NOT EXISTS vulnerability_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    vuln_id VARCHAR(255),
    vuln_type ENUM('web', 'network', 'system', 'database', 'application', 'configuration') NOT NULL,
    vuln_name VARCHAR(500) NOT NULL,
    severity ENUM('critical', 'high', 'medium', 'low', 'info', 'unknown') DEFAULT 'unknown',
    cvss_score DECIMAL(3,1) DEFAULT 0.0,
    cve_id VARCHAR(50),
    description TEXT,
    solution TEXT,
    vuln_references JSON,
    exploit_available BOOLEAN DEFAULT FALSE,
    verified BOOLEAN DEFAULT FALSE,
    false_positive BOOLEAN DEFAULT FALSE,
    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_verified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    vuln_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_asset_id (asset_id),
    INDEX idx_vuln_type (vuln_type),
    INDEX idx_severity (severity),
    INDEX idx_cve_id (cve_id),
    INDEX idx_verified (verified),
    INDEX idx_false_positive (false_positive),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. 威胁情报表（已存在，确保字段完整）
CREATE TABLE IF NOT EXISTS threat_intelligence (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    indicator_type ENUM('ip', 'domain', 'url', 'hash', 'email', 'file') NOT NULL,
    indicator_value VARCHAR(2048) NOT NULL,
    threat_type VARCHAR(100),
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    severity ENUM('critical', 'high', 'medium', 'low', 'info', 'unknown') DEFAULT 'unknown',
    source VARCHAR(255),
    description TEXT,
    first_seen TIMESTAMP NULL,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ioc_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_asset_id (asset_id),
    INDEX idx_indicator_type_value (indicator_type, indicator_value(255)),
    INDEX idx_threat_type (threat_type),
    INDEX idx_severity (severity),
    INDEX idx_source (source),
    INDEX idx_last_seen (last_seen),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. 扫描任务表
CREATE TABLE IF NOT EXISTS scan_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_name VARCHAR(255) NOT NULL,
    task_type ENUM('comprehensive', 'awvs', 'database', 'network', 'web') NOT NULL,
    target_list JSON NOT NULL,
    scan_config JSON,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    progress DECIMAL(5,2) DEFAULT 0.00,
    start_time TIMESTAMP NULL,
    end_time TIMESTAMP NULL,
    duration INT,
    results_summary JSON,
    error_message TEXT,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 8. 扫描结果关联表
CREATE TABLE IF NOT EXISTS scan_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id BIGINT,
    asset_id BIGINT,
    scan_type VARCHAR(50) NOT NULL,
    result_data JSON,
    scan_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_time INT,
    status ENUM('success', 'failed', 'partial') DEFAULT 'success',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_task_id (task_id),
    INDEX idx_asset_id (asset_id),
    INDEX idx_scan_type (scan_type),
    INDEX idx_scan_time (scan_time),
    INDEX idx_status (status),
    FOREIGN KEY (task_id) REFERENCES scan_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 9. 数据库扫描结果表（专门存储数据库扫描信息）
CREATE TABLE IF NOT EXISTS database_scan_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    database_type VARCHAR(50) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INT NOT NULL,
    version VARCHAR(255),
    authentication_info JSON,
    databases_list JSON,
    users_list JSON,
    configuration_info JSON,
    vulnerabilities JSON,
    security_issues JSON,
    recommendations JSON,
    scan_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scan_duration DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_asset_id (asset_id),
    INDEX idx_database_type (database_type),
    INDEX idx_host_port (host, port),
    INDEX idx_scan_time (scan_time),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 10. 确保domain_ip_mapping表存在（兼容现有AWVS扫描）
CREATE TABLE IF NOT EXISTS domain_ip_mapping (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    domain VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    awvs_status ENUM('todo', 'scanning', 'done', 'failed') DEFAULT 'todo',
    awvs_scan_id VARCHAR(255),
    awvs_result JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_domain (domain),
    INDEX idx_awvs_status (awvs_status),
    INDEX idx_awvs_scan_id (awvs_scan_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建视图：资产概览
CREATE OR REPLACE VIEW asset_overview AS
SELECT 
    ta.id,
    ta.asset_type,
    ta.asset_value,
    ta.asset_name,
    ta.status,
    ta.last_scan_time,
    COUNT(DISTINCT si.id) as service_count,
    COUNT(DISTINCT vi.id) as vulnerability_count,
    COUNT(DISTINCT wai.id) as web_app_count,
    MAX(vi.severity) as max_severity
FROM target_assets ta
LEFT JOIN service_info si ON ta.id = si.asset_id
LEFT JOIN vulnerability_info vi ON ta.id = vi.asset_id AND vi.false_positive = FALSE
LEFT JOIN web_app_info wai ON ta.id = wai.asset_id
GROUP BY ta.id, ta.asset_type, ta.asset_value, ta.asset_name, ta.status, ta.last_scan_time;

-- 创建视图：漏洞统计
CREATE OR REPLACE VIEW vulnerability_statistics AS
SELECT 
    vuln_type,
    severity,
    COUNT(*) as count,
    COUNT(DISTINCT asset_id) as affected_assets
FROM vulnerability_info 
WHERE false_positive = FALSE
GROUP BY vuln_type, severity
ORDER BY 
    FIELD(severity, 'critical', 'high', 'medium', 'low', 'info', 'unknown'),
    vuln_type;

-- 创建存储过程：清理过期数据
DELIMITER //
CREATE PROCEDURE CleanupOldData(IN days_to_keep INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 删除过期的扫描任务（保留最近的数据）
    DELETE FROM scan_tasks 
    WHERE status IN ('completed', 'failed', 'cancelled') 
    AND created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 删除孤立的扫描结果
    DELETE sr FROM scan_results sr
    LEFT JOIN scan_tasks st ON sr.task_id = st.id
    WHERE st.id IS NULL;
    
    -- 删除非活跃资产的旧记录
    DELETE FROM target_assets 
    WHERE status = 'archived' 
    AND updated_at < DATE_SUB(NOW(), INTERVAL days_to_keep * 2 DAY);
    
    COMMIT;
END //
DELIMITER ;

-- 插入默认配置数据
INSERT IGNORE INTO target_assets (asset_type, asset_value, asset_name, description, status) 
VALUES ('network', '127.0.0.1/32', 'Localhost', '本地回环网络', 'active');

-- 创建索引优化查询性能
CREATE INDEX idx_vulnerability_severity_asset ON vulnerability_info(severity, asset_id);
CREATE INDEX idx_service_port_state ON service_info(port, state);
CREATE INDEX idx_web_app_cms ON web_app_info(cms_type, cms_version);
CREATE INDEX idx_threat_intel_confidence ON threat_intelligence(confidence_score, severity);

-- 设置表的字符集和排序规则
ALTER TABLE target_assets CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE network_info CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE service_info CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE web_app_info CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE vulnerability_info CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE threat_intelligence CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE domain_ip_mapping CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
