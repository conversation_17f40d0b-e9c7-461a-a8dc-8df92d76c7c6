#从网段任务中取数据来进行一个个IP的nmap扫描
import os
import nmap
import ipaddress
import json
import pymysql
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import configparser  # 新增导入

class NmapScanner:
    def __init__(self):
        config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')  # 指定编码为utf-8
        if not config.has_section('mysql'):
            raise ValueError("MySQL section not found in config file")
        
        # 获取数据库配置
        db_config = {
            'host': config['mysql']['host'],
            'port': int(config['mysql']['port']),
            'user': config['mysql']['user'],
            'password': config['mysql']['password'],
            'database': config['mysql']['database'],
            'connect_timeout': 60,  # 增加连接超时设置
            'read_timeout': 60,     # 增加读取超时设置
            'write_timeout': 60     # 增加写入超时设置
        }
        
        self.db = pymysql.connect(**db_config)  # 使用pymysql.connect而不是pymysql.connector.connect
        self.cursor = self.db.cursor()
        self.nm = nmap.PortScanner()
        self.executor = ThreadPoolExecutor(max_workers=10)
    
    #获取要扫描的网段
    def _get_todo_scans(self):
        self.db.ping(reconnect=True)  # 检查连接是否有效，如果无效则重新连接
        cursor = self.db.cursor()  # 使用dictionary=True以字典形式返回数据

        query = "SELECT ip_segment FROM ip_segment_task WHERE status = 'todo' limit 1"
        cursor.execute(query)
        ip_segment_todo=cursor.fetchone()
        if ip_segment_todo:
            return ip_segment_todo
        else:
            #如果没有要扫描的网段，退出程序
            print('no ip segment to scan')
            exit(0)


    def _update_scan_status(self, ip_range, status):
        self.db.ping(reconnect=True)  # 检查连接是否有效，如果无效则重新连接
        cursor = self.db.cursor()
        # 修改字段名从 ip_range 改为 ip_segment
        query = "UPDATE ip_segment_task SET status = %s WHERE ip_segment = %s"
        cursor.execute(query, (status, ip_range))
        self.db.commit()

    def _save_scan_result(self, ip, scan_result):
        self.db.ping(reconnect=True)  # 检查连接是否有效，如果无效则重新连接
        cursor = self.db.cursor()
        for protocol in scan_result['scan'][ip]:
            # 检查是否为字典类型
            if isinstance(scan_result['scan'][ip][protocol], dict):
                for port, port_info in scan_result['scan'][ip][protocol].items():
                    # 确保port_info是字典类型
                    if not isinstance(port_info, dict):
                        print(f"端口信息不是字典类型，跳过... 类型: {type(port_info)}, 内容: {port_info}")
                        continue
                        
                    query = """
                    INSERT INTO nmap_scan_results 
                    (ip_address, port, protocol, service_name, service_version, state, scan_time)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    service_name = VALUES(service_name),
                    service_version = VALUES(service_version),
                    state = VALUES(state),
                    scan_time = VALUES(scan_time)
                    """
                    values = (
                        ip, port, protocol,
                        port_info.get('name', ''), port_info.get('version', ''),
                        port_info.get('state', 'unknown'), datetime.now()
                    )
                    cursor.execute(query, values)
            # 如果是列表类型，使用不同的处理方式
            elif isinstance(scan_result['scan'][ip][protocol], list):
                print(f"协议 {protocol} 的结果是列表类型，内容: {scan_result['scan'][ip][protocol]}")
                # 这里可以根据实际数据结构添加处理列表的代码
        self.db.commit()

    def scan_ip_range(self, ip_range):
        try:
            network = ipaddress.ip_network(ip_range)
            for ip in network.hosts():
                #获取task_tmp数据库下 task_type为nmap的task_tmp数据
                #如果ip小于tasktmp，那么跳过，否则执行扫描
                #扫描完成之后，更新task_tmp数据
                last_scanned=''
                ip_str = str(ip)
                try:
                    # 获取task_tmp数据库下 task_type为nmap的task_tmp数据
                    self.db.ping(reconnect=True)  # 检查连接是否有效，如果无效则重新连接
                    cursor = self.db.cursor()
                    query = "SELECT task_tmp FROM task_tmp WHERE task_type = 'nmap' "
                    cursor.execute(query)
                    last_scanned = cursor.fetchone()

                except Exception as e:
                    print(f"获取最后扫描的IP时出错: {e}")

                # 如果存在最后扫描IP且在同一网段，并且当前IP小于等于最后扫描IP，则跳过
                if last_scanned and last_scanned[0]:
                    try:
                        # 自动识别IPv4/IPv6地址
                        current_ip = ipaddress.ip_address(ip_str)
                        last_ip = ipaddress.ip_address(last_scanned[0])

                        # 检查是否在同一网段(自动识别IPv4/IPv6网络)
                        current_network = ipaddress.ip_network(ip_range, strict=False)
                        if current_ip in current_network and last_ip in current_network:
                            if int(current_ip) <= int(last_ip):
                                print(f"跳过已扫描的IP: {ip_str} (最后扫描IP: {last_scanned[0]})")
                                continue
                    except (ipaddress.AddressValueError, ValueError) as e:
                        print(f"IP地址处理错误: {e}, 继续执行扫描")
                        pass


                # 将网段切分成ip之后进行nmap扫描
                self._scan_single_ip(ip_str)
                
                # 扫描完成之后，更新task_tmp数据
                try:
                    update_query = """
                    INSERT INTO task_tmp (task_type, task_tmp) 
                    VALUES (%s, %s)
                    ON DUPLICATE KEY UPDATE task_tmp = VALUES(task_tmp)
                    """
                    cursor.execute(update_query, ('nmap', ip_str))
                    self.db.commit()
                except Exception as e:
                    print(f"更新task_tmp数据时出错: {e}")
                    self.db.rollback()

            # 扫描完成之后将扫描状态改为完成
            self._update_scan_status(ip_range, 'done')
        except ValueError as e:
            print(f"Invalid IP range: {e}")

    #保存完整扫描结果到数据库
    def _save_scan_json_record(self, ip, result):
        self.db.ping(reconnect=True)  # 检查连接是否有效，如果无效则重新连接
        cursor = self.db.cursor()
        query = """
        INSERT INTO nmap_scan_json_result (ip_address, result)
        VALUES (%s, %s)
        ON DUPLICATE KEY UPDATE
        result = VALUES(result)
        """
        # 将Python字典转换为JSON字符串
        result_json = json.dumps(result)
        print(query)
        print(ip, "JSON数据已转换")
        try:
            cursor.execute(query, (ip, result_json))
            self.db.commit()
        except Exception as e:
            print(f"保存扫描结果到数据库时出错: {e}")
            self.db.rollback()
            raise e

    #开始扫描
    def start_scanning(self):
        #获取要扫描的对象
        todo_scan = self._get_todo_scans()[0]
        print(todo_scan)
        #根据要扫描的网段传入扫描，先将网段切成单独IP然后调用nmap扫描
        self.scan_ip_range(todo_scan)
        

    def _scan_single_ip(self, single_ip):
        try:
            # 先进行主机发现扫描，不使用-Pn参数
            print(f"主机发现扫描 {single_ip}...")
            host_discovery = self.nm.scan(single_ip, arguments='-PE -PP -PS21,22,23,25,80,443 -PA80,443 -PU53,67,68 -PY -sn')
            print('host_discovery',host_discovery)
            
            # 检查主机是否真正存活
            host_is_up = False
            if single_ip in host_discovery['scan']:
                status = host_discovery['scan'][single_ip].get('status', {})
                if isinstance(status, dict) and status.get('state') == 'up':
                    host_is_up = True
                    print(f"主机 {single_ip} 存活，继续进行端口扫描...")
                else:
                    print(f"主机 {single_ip} 不存活，跳过详细扫描...")
                    return
            else:
                print(f"主机 {single_ip} 不在扫描结果中，跳过详细扫描...")
                return

            if host_is_up:
                print(f"进行详细扫描...", single_ip)
                arguments = '-A -T4 -v --reason --script=default,safe --osscan-guess --version-all'
                scan_result = self.nm.scan(single_ip, arguments=arguments)
            else:
                print(f"快速扫描未返回目标IP的结果，跳过详细扫描...")
                return
            
            # 保存扫描结果
            if single_ip in scan_result['scan']:
                ip_info = scan_result['scan'][single_ip]
                # 先检查ip_info是否为字典类型
                if isinstance(ip_info, dict):
                    print(f"扫描结果结构: {json.dumps(ip_info, indent=2)}...")
                    self._save_scan_json_record(single_ip, scan_result)
                    # self._save_scan_result(single_ip, scan_result)
                else:
                    print(f"目标IP {single_ip} 的扫描结果不是字典类型，跳过... 类型: {type(ip_info)}")
                    print(f"扫描结果内容: {ip_info}")
            else:
                print(f"目标IP {single_ip} 不在扫描结果中，跳过...")
        except Exception as e:
            print(f"扫描出错: {e}")
            # 打印更详细的错误信息
            import traceback
            traceback.print_exc()


    def close(self):
        self.executor.shutdown(wait=True)
        self.db.close()


def main():
    scanner = NmapScanner()
    while True:
        try:
            scanner.start_scanning()
        except Exception as e:
            print(f"扫描过程中发生错误: {e}")
            # 发生错误时重新初始化扫描器
            scanner.close()
            scanner = NmapScanner()
            continue
        finally:
            try:
                if scanner.db.open:  # 检查连接是否仍然打开
                    scanner.close()
            except:
                pass  # 如果检查连接状态时出错，忽略

main()