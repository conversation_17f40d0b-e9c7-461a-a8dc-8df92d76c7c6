"""
子域名枚举和DNS深度分析器

缺失功能：
1. 子域名枚举 - 字典爆破、DNS记录查询、证书透明度
2. DNS记录深度分析 - A/AAAA/CNAME/MX/TXT/NS/SOA记录
3. DNS安全检测 - DNS劫持、缓存投毒、DNSSEC
4. 域名历史分析 - 历史解析记录、域名变更
5. 域名注册信息 - Whois查询、注册商信息
6. DNS服务器分析 - 权威服务器、递归查询
7. 域名接管检测 - 悬挂域名、子域名接管

作者：AI渗透测试系统
版本：1.0.0
"""

import dns.resolver
import dns.zone
import dns.query
import dns.reversename
import requests
import socket
import json
import time
import threading
from typing import Dict, List, Any, Optional, Set
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import re
import whois
from urllib.parse import urlparse
import ssl
import subprocess

logger = logging.getLogger(__name__)


class SubdomainEnumerator:
    """子域名枚举器"""
    
    def __init__(self):
        self.common_subdomains = [
            'www', 'mail', 'ftp', 'admin', 'test', 'dev', 'staging', 'api', 'app',
            'blog', 'shop', 'store', 'support', 'help', 'docs', 'cdn', 'static',
            'img', 'images', 'assets', 'js', 'css', 'media', 'upload', 'download',
            'secure', 'ssl', 'vpn', 'remote', 'portal', 'dashboard', 'panel',
            'cpanel', 'webmail', 'email', 'smtp', 'pop', 'imap', 'ns1', 'ns2',
            'dns', 'mx', 'mx1', 'mx2', 'autodiscover', 'autoconfig', 'sip',
            'voip', 'chat', 'forum', 'community', 'social', 'wiki', 'kb',
            'mobile', 'm', 'wap', 'touch', 'beta', 'alpha', 'demo', 'preview',
            'old', 'new', 'v1', 'v2', 'v3', 'backup', 'bak', 'temp', 'tmp'
        ]
        
        self.dns_servers = [
            '*******',      # Google
            '*******',      # Cloudflare
            '**************', # OpenDNS
            '***************' # 114 DNS
        ]
        
        self.ct_logs_api = "https://crt.sh/?q={}&output=json"
        
    def enumerate_subdomains(self, domain: str) -> Dict[str, Any]:
        """
        枚举子域名
        
        Args:
            domain: 目标域名
            
        Returns:
            Dict: 子域名枚举结果
        """
        logger.info(f"开始枚举子域名：{domain}")
        
        results = {
            'domain': domain,
            'scan_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'subdomains': set(),
            'methods': {
                'dictionary_bruteforce': [],
                'certificate_transparency': [],
                'dns_records': [],
                'search_engines': [],
                'zone_transfer': []
            },
            'statistics': {}
        }
        
        try:
            # 方法1：字典爆破
            dict_results = self._dictionary_bruteforce(domain)
            results['methods']['dictionary_bruteforce'] = dict_results
            results['subdomains'].update(dict_results)
            
            # 方法2：证书透明度日志
            ct_results = self._certificate_transparency_search(domain)
            results['methods']['certificate_transparency'] = ct_results
            results['subdomains'].update(ct_results)
            
            # 方法3：DNS记录查询
            dns_results = self._dns_record_enumeration(domain)
            results['methods']['dns_records'] = dns_results
            results['subdomains'].update(dns_results)
            
            # 方法4：搜索引擎
            search_results = self._search_engine_enumeration(domain)
            results['methods']['search_engines'] = search_results
            results['subdomains'].update(search_results)
            
            # 方法5：DNS区域传输
            zone_results = self._zone_transfer_attempt(domain)
            results['methods']['zone_transfer'] = zone_results
            results['subdomains'].update(zone_results)
            
            # 转换为列表并验证
            results['subdomains'] = list(results['subdomains'])
            results['verified_subdomains'] = self._verify_subdomains(results['subdomains'])
            
            # 统计信息
            results['statistics'] = {
                'total_found': len(results['subdomains']),
                'verified_count': len(results['verified_subdomains']),
                'method_counts': {
                    method: len(subdomains) for method, subdomains in results['methods'].items()
                }
            }
            
        except Exception as e:
            logger.error(f"子域名枚举失败：{e}")
            results['error'] = str(e)
        
        return results
    
    def _dictionary_bruteforce(self, domain: str) -> List[str]:
        """字典爆破子域名"""
        logger.info(f"开始字典爆破：{domain}")
        found_subdomains = []
        
        def check_subdomain(subdomain):
            full_domain = f"{subdomain}.{domain}"
            try:
                answers = dns.resolver.resolve(full_domain, 'A')
                if answers:
                    found_subdomains.append(full_domain)
                    logger.debug(f"发现子域名：{full_domain}")
            except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer, dns.resolver.Timeout):
                pass
            except Exception as e:
                logger.debug(f"查询失败 {full_domain}：{e}")
        
        # 并发查询
        with ThreadPoolExecutor(max_workers=50) as executor:
            futures = [executor.submit(check_subdomain, sub) for sub in self.common_subdomains]
            for future in as_completed(futures):
                try:
                    future.result(timeout=5)
                except Exception as e:
                    logger.debug(f"字典爆破任务失败：{e}")
        
        return found_subdomains
    
    def _certificate_transparency_search(self, domain: str) -> List[str]:
        """证书透明度日志搜索"""
        logger.info(f"开始证书透明度搜索：{domain}")
        found_subdomains = []
        
        try:
            # 查询crt.sh
            url = self.ct_logs_api.format(f"%.{domain}")
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                ct_data = response.json()
                
                for entry in ct_data:
                    name_value = entry.get('name_value', '')
                    # 解析证书中的域名
                    domains = name_value.split('\n')
                    for cert_domain in domains:
                        cert_domain = cert_domain.strip()
                        if cert_domain.endswith(f".{domain}") and cert_domain not in found_subdomains:
                            found_subdomains.append(cert_domain)
                
                # 去重并过滤
                found_subdomains = list(set(found_subdomains))
                found_subdomains = [d for d in found_subdomains if self._is_valid_subdomain(d, domain)]
        
        except Exception as e:
            logger.error(f"证书透明度搜索失败：{e}")
        
        return found_subdomains
    
    def _dns_record_enumeration(self, domain: str) -> List[str]:
        """DNS记录枚举"""
        logger.info(f"开始DNS记录枚举：{domain}")
        found_subdomains = []
        
        try:
            # 查询不同类型的DNS记录
            record_types = ['A', 'AAAA', 'CNAME', 'MX', 'NS', 'TXT', 'SRV']
            
            for record_type in record_types:
                try:
                    answers = dns.resolver.resolve(domain, record_type)
                    for answer in answers:
                        # 从记录中提取可能的子域名
                        record_data = str(answer)
                        potential_domains = self._extract_domains_from_record(record_data, domain)
                        found_subdomains.extend(potential_domains)
                
                except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer):
                    continue
                except Exception as e:
                    logger.debug(f"DNS记录查询失败 {domain} {record_type}：{e}")
            
            # 查询通配符记录
            wildcard_domains = self._check_wildcard_dns(domain)
            found_subdomains.extend(wildcard_domains)
        
        except Exception as e:
            logger.error(f"DNS记录枚举失败：{e}")
        
        return list(set(found_subdomains))
    
    def _search_engine_enumeration(self, domain: str) -> List[str]:
        """搜索引擎枚举"""
        logger.info(f"开始搜索引擎枚举：{domain}")
        found_subdomains = []
        
        try:
            # Google搜索
            google_results = self._google_search_subdomains(domain)
            found_subdomains.extend(google_results)
            
            # Bing搜索
            bing_results = self._bing_search_subdomains(domain)
            found_subdomains.extend(bing_results)
            
        except Exception as e:
            logger.error(f"搜索引擎枚举失败：{e}")
        
        return list(set(found_subdomains))
    
    def _zone_transfer_attempt(self, domain: str) -> List[str]:
        """尝试DNS区域传输"""
        logger.info(f"尝试DNS区域传输：{domain}")
        found_subdomains = []
        
        try:
            # 获取权威DNS服务器
            ns_records = dns.resolver.resolve(domain, 'NS')
            
            for ns in ns_records:
                ns_server = str(ns).rstrip('.')
                try:
                    # 尝试区域传输
                    zone = dns.zone.from_xfr(dns.query.xfr(ns_server, domain))
                    
                    for name, node in zone.nodes.items():
                        if name != dns.name.from_text('@'):
                            subdomain = f"{name}.{domain}"
                            found_subdomains.append(subdomain)
                    
                    logger.warning(f"DNS区域传输成功：{ns_server}")
                    break  # 如果成功，不需要尝试其他服务器
                
                except Exception as e:
                    logger.debug(f"区域传输失败 {ns_server}：{e}")
        
        except Exception as e:
            logger.debug(f"获取NS记录失败：{e}")
        
        return found_subdomains
    
    def _google_search_subdomains(self, domain: str) -> List[str]:
        """Google搜索子域名"""
        found_subdomains = []
        
        try:
            # 构造搜索查询
            query = f"site:{domain} -www.{domain}"
            # 注意：实际使用时需要处理Google的反爬虫机制
            # 这里只是示例代码
            
            # 模拟搜索结果解析
            # 实际实现需要使用合适的搜索API或爬虫技术
            
        except Exception as e:
            logger.debug(f"Google搜索失败：{e}")
        
        return found_subdomains
    
    def _bing_search_subdomains(self, domain: str) -> List[str]:
        """Bing搜索子域名"""
        found_subdomains = []
        
        try:
            # 类似Google搜索的实现
            pass
        except Exception as e:
            logger.debug(f"Bing搜索失败：{e}")
        
        return found_subdomains
    
    def _extract_domains_from_record(self, record_data: str, base_domain: str) -> List[str]:
        """从DNS记录中提取域名"""
        domains = []
        
        # 使用正则表达式匹配域名模式
        domain_pattern = r'([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)*' + re.escape(base_domain)
        matches = re.findall(domain_pattern, record_data)
        
        for match in matches:
            if isinstance(match, tuple):
                domain = match[0] + base_domain
            else:
                domain = match
            
            if self._is_valid_subdomain(domain, base_domain):
                domains.append(domain)
        
        return domains
    
    def _check_wildcard_dns(self, domain: str) -> List[str]:
        """检查通配符DNS记录"""
        wildcard_domains = []
        
        try:
            # 测试随机子域名
            random_subdomain = f"random-test-{int(time.time())}.{domain}"
            answers = dns.resolver.resolve(random_subdomain, 'A')
            
            if answers:
                # 如果随机子域名有解析，说明存在通配符记录
                logger.warning(f"检测到通配符DNS记录：{domain}")
                wildcard_domains.append(f"*.{domain}")
        
        except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer):
            # 这是正常情况，没有通配符记录
            pass
        except Exception as e:
            logger.debug(f"通配符DNS检查失败：{e}")
        
        return wildcard_domains
    
    def _is_valid_subdomain(self, subdomain: str, base_domain: str) -> bool:
        """验证子域名是否有效"""
        if not subdomain or not subdomain.endswith(f".{base_domain}"):
            return False
        
        # 检查是否包含无效字符
        if any(char in subdomain for char in ['*', ' ', '\t', '\n']):
            return False
        
        return True
    
    def _verify_subdomains(self, subdomains: List[str]) -> List[Dict[str, Any]]:
        """验证子域名的有效性"""
        verified = []
        
        def verify_single(subdomain):
            try:
                # DNS解析验证
                answers = dns.resolver.resolve(subdomain, 'A')
                ip_addresses = [str(answer) for answer in answers]
                
                # HTTP服务检测
                http_status = self._check_http_service(subdomain)
                
                verified.append({
                    'subdomain': subdomain,
                    'ip_addresses': ip_addresses,
                    'http_status': http_status,
                    'verified': True
                })
                
            except Exception as e:
                logger.debug(f"子域名验证失败 {subdomain}：{e}")
        
        # 并发验证
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(verify_single, sub) for sub in subdomains]
            for future in as_completed(futures):
                try:
                    future.result(timeout=10)
                except Exception as e:
                    logger.debug(f"验证任务失败：{e}")
        
        return verified
    
    def _check_http_service(self, subdomain: str) -> Dict[str, Any]:
        """检查HTTP服务"""
        http_info = {
            'http_accessible': False,
            'https_accessible': False,
            'http_status': None,
            'https_status': None,
            'redirects_to': None
        }
        
        # 检查HTTP
        try:
            response = requests.head(f"http://{subdomain}", timeout=10, allow_redirects=False)
            http_info['http_accessible'] = True
            http_info['http_status'] = response.status_code
            
            if 'location' in response.headers:
                http_info['redirects_to'] = response.headers['location']
        except:
            pass
        
        # 检查HTTPS
        try:
            response = requests.head(f"https://{subdomain}", timeout=10, allow_redirects=False, verify=False)
            http_info['https_accessible'] = True
            http_info['https_status'] = response.status_code
        except:
            pass
        
        return http_info


class DNSAnalyzer:
    """DNS深度分析器"""
    
    def __init__(self):
        self.record_types = ['A', 'AAAA', 'CNAME', 'MX', 'NS', 'TXT', 'SOA', 'SRV', 'PTR']
    
    def analyze_dns(self, domain: str) -> Dict[str, Any]:
        """
        DNS深度分析
        
        Args:
            domain: 目标域名
            
        Returns:
            Dict: DNS分析结果
        """
        logger.info(f"开始DNS深度分析：{domain}")
        
        dns_info = {
            'domain': domain,
            'scan_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'dns_records': {},
            'nameservers': [],
            'mx_records': [],
            'security_analysis': {},
            'performance_analysis': {},
            'whois_info': {}
        }
        
        try:
            # DNS记录查询
            dns_info['dns_records'] = self._query_all_records(domain)
            
            # 权威DNS服务器分析
            dns_info['nameservers'] = self._analyze_nameservers(domain)
            
            # MX记录分析
            dns_info['mx_records'] = self._analyze_mx_records(domain)
            
            # DNS安全分析
            dns_info['security_analysis'] = self._analyze_dns_security(domain)
            
            # DNS性能分析
            dns_info['performance_analysis'] = self._analyze_dns_performance(domain)
            
            # Whois信息
            dns_info['whois_info'] = self._get_whois_info(domain)
            
        except Exception as e:
            logger.error(f"DNS分析失败：{e}")
            dns_info['error'] = str(e)
        
        return dns_info
    
    def _query_all_records(self, domain: str) -> Dict[str, List[str]]:
        """查询所有DNS记录类型"""
        records = {}
        
        for record_type in self.record_types:
            try:
                answers = dns.resolver.resolve(domain, record_type)
                records[record_type] = [str(answer) for answer in answers]
            except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer):
                records[record_type] = []
            except Exception as e:
                logger.debug(f"查询{record_type}记录失败：{e}")
                records[record_type] = []
        
        return records
    
    def _analyze_nameservers(self, domain: str) -> List[Dict[str, Any]]:
        """分析权威DNS服务器"""
        nameservers = []
        
        try:
            ns_records = dns.resolver.resolve(domain, 'NS')
            
            for ns in ns_records:
                ns_name = str(ns).rstrip('.')
                ns_info = {
                    'name': ns_name,
                    'ip_addresses': [],
                    'response_time': None,
                    'software': None
                }
                
                # 解析NS服务器IP
                try:
                    ns_ips = dns.resolver.resolve(ns_name, 'A')
                    ns_info['ip_addresses'] = [str(ip) for ip in ns_ips]
                except:
                    pass
                
                # 测试响应时间
                ns_info['response_time'] = self._test_dns_response_time(ns_name, domain)
                
                nameservers.append(ns_info)
        
        except Exception as e:
            logger.debug(f"NS记录分析失败：{e}")
        
        return nameservers
    
    def _analyze_mx_records(self, domain: str) -> List[Dict[str, Any]]:
        """分析MX记录"""
        mx_records = []
        
        try:
            mx_answers = dns.resolver.resolve(domain, 'MX')
            
            for mx in mx_answers:
                mx_info = {
                    'priority': mx.preference,
                    'exchange': str(mx.exchange).rstrip('.'),
                    'ip_addresses': [],
                    'smtp_banner': None
                }
                
                # 解析MX服务器IP
                try:
                    mx_ips = dns.resolver.resolve(mx_info['exchange'], 'A')
                    mx_info['ip_addresses'] = [str(ip) for ip in mx_ips]
                except:
                    pass
                
                # 获取SMTP Banner
                mx_info['smtp_banner'] = self._get_smtp_banner(mx_info['exchange'])
                
                mx_records.append(mx_info)
        
        except Exception as e:
            logger.debug(f"MX记录分析失败：{e}")
        
        return mx_records
    
    def _analyze_dns_security(self, domain: str) -> Dict[str, Any]:
        """DNS安全分析"""
        security_info = {
            'dnssec_enabled': False,
            'dns_over_https': False,
            'dns_over_tls': False,
            'cache_poisoning_vulnerable': False,
            'zone_transfer_allowed': False
        }
        
        try:
            # DNSSEC检查
            security_info['dnssec_enabled'] = self._check_dnssec(domain)
            
            # DNS over HTTPS/TLS检查
            security_info['dns_over_https'] = self._check_doh_support(domain)
            security_info['dns_over_tls'] = self._check_dot_support(domain)
            
            # 区域传输检查
            security_info['zone_transfer_allowed'] = self._check_zone_transfer_vulnerability(domain)
            
        except Exception as e:
            logger.debug(f"DNS安全分析失败：{e}")
        
        return security_info
    
    def _analyze_dns_performance(self, domain: str) -> Dict[str, Any]:
        """DNS性能分析"""
        performance_info = {
            'average_response_time': None,
            'fastest_nameserver': None,
            'slowest_nameserver': None,
            'response_times': []
        }
        
        try:
            # 测试所有NS服务器的响应时间
            ns_records = dns.resolver.resolve(domain, 'NS')
            response_times = []
            
            for ns in ns_records:
                ns_name = str(ns).rstrip('.')
                response_time = self._test_dns_response_time(ns_name, domain)
                if response_time:
                    response_times.append({
                        'nameserver': ns_name,
                        'response_time': response_time
                    })
            
            if response_times:
                performance_info['response_times'] = response_times
                performance_info['average_response_time'] = sum(rt['response_time'] for rt in response_times) / len(response_times)
                performance_info['fastest_nameserver'] = min(response_times, key=lambda x: x['response_time'])
                performance_info['slowest_nameserver'] = max(response_times, key=lambda x: x['response_time'])
        
        except Exception as e:
            logger.debug(f"DNS性能分析失败：{e}")
        
        return performance_info
    
    def _get_whois_info(self, domain: str) -> Dict[str, Any]:
        """获取Whois信息"""
        whois_info = {}
        
        try:
            w = whois.whois(domain)
            whois_info = {
                'registrar': w.registrar,
                'creation_date': str(w.creation_date) if w.creation_date else None,
                'expiration_date': str(w.expiration_date) if w.expiration_date else None,
                'updated_date': str(w.updated_date) if w.updated_date else None,
                'status': w.status,
                'nameservers': w.name_servers,
                'registrant_country': w.country
            }
        except Exception as e:
            logger.debug(f"Whois查询失败：{e}")
            whois_info['error'] = str(e)
        
        return whois_info
    
    def _test_dns_response_time(self, nameserver: str, domain: str) -> Optional[float]:
        """测试DNS响应时间"""
        try:
            resolver = dns.resolver.Resolver()
            resolver.nameservers = [socket.gethostbyname(nameserver)]
            resolver.timeout = 5
            
            start_time = time.time()
            resolver.resolve(domain, 'A')
            end_time = time.time()
            
            return (end_time - start_time) * 1000  # 转换为毫秒
        except Exception as e:
            logger.debug(f"DNS响应时间测试失败 {nameserver}：{e}")
            return None
    
    def _get_smtp_banner(self, mx_server: str) -> Optional[str]:
        """获取SMTP Banner"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((mx_server, 25))
            banner = sock.recv(1024).decode('utf-8').strip()
            sock.close()
            return banner
        except Exception as e:
            logger.debug(f"SMTP Banner获取失败 {mx_server}：{e}")
            return None
    
    def _check_dnssec(self, domain: str) -> bool:
        """检查DNSSEC"""
        try:
            # 查询DNSKEY记录
            dns.resolver.resolve(domain, 'DNSKEY')
            return True
        except:
            return False
    
    def _check_doh_support(self, domain: str) -> bool:
        """检查DNS over HTTPS支持"""
        # 简化实现
        return False
    
    def _check_dot_support(self, domain: str) -> bool:
        """检查DNS over TLS支持"""
        # 简化实现
        return False
    
    def _check_zone_transfer_vulnerability(self, domain: str) -> bool:
        """检查区域传输漏洞"""
        try:
            ns_records = dns.resolver.resolve(domain, 'NS')
            for ns in ns_records:
                ns_server = str(ns).rstrip('.')
                try:
                    dns.zone.from_xfr(dns.query.xfr(ns_server, domain))
                    return True  # 如果成功，说明存在漏洞
                except:
                    continue
        except:
            pass
        return False


# 使用示例
if __name__ == "__main__":
    # 子域名枚举
    enumerator = SubdomainEnumerator()
    subdomain_results = enumerator.enumerate_subdomains("example.com")
    print("子域名枚举结果：")
    print(json.dumps(subdomain_results, indent=2, ensure_ascii=False))
    
    # DNS分析
    analyzer = DNSAnalyzer()
    dns_results = analyzer.analyze_dns("example.com")
    print("\nDNS分析结果：")
    print(json.dumps(dns_results, indent=2, ensure_ascii=False))
