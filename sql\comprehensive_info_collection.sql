-- 全面信息收集数据库表结构
-- 支持全自动全智能全网漏洞分析器的信息存储需求

-- 1. 目标资产表
CREATE TABLE IF NOT EXISTS target_assets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_type ENUM('ip', 'domain', 'url', 'service', 'network') NOT NULL,
    asset_value VARCHAR(2048) NOT NULL,
    asset_name VARCHAR(255),
    description TEXT,
    business_importance ENUM('critical', 'high', 'medium', 'low') DEFAULT 'medium',
    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_scan_time TIMESTAMP NULL,
    status ENUM('active', 'inactive', 'unknown', 'archived') DEFAULT 'unknown',
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_asset_type_value (asset_type, asset_value(255)),
    INDEX idx_last_scan (last_scan_time),
    INDEX idx_status (status),
    UNIQUE KEY unique_asset (asset_type, asset_value(255))
);

-- 2. 网络信息表
CREATE TABLE IF NOT EXISTS network_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    ip_address VARCHAR(45) NOT NULL,
    mac_address VARCHAR(17),
    hostname VARCHAR(255),
    os_type VARCHAR(100),
    os_version VARCHAR(255),
    os_accuracy INT DEFAULT 0,
    network_segment VARCHAR(45),
    gateway VARCHAR(45),
    response_time INT,
    ttl INT,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scan_method VARCHAR(50),
    scan_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ip (ip_address),
    INDEX idx_asset_id (asset_id),
    INDEX idx_network_segment (network_segment),
    INDEX idx_last_seen (last_seen),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
);

-- 3. 服务信息表
CREATE TABLE IF NOT EXISTS service_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    ip_address VARCHAR(45) NOT NULL,
    port INT NOT NULL,
    protocol ENUM('tcp', 'udp', 'sctp') NOT NULL,
    service_name VARCHAR(100),
    service_version VARCHAR(255),
    service_product VARCHAR(255),
    service_banner TEXT,
    service_extrainfo TEXT,
    state ENUM('open', 'closed', 'filtered', 'unfiltered', 'open|filtered', 'closed|filtered') NOT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_verified TIMESTAMP NULL,
    service_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ip_port (ip_address, port, protocol),
    INDEX idx_asset_id (asset_id),
    INDEX idx_service (service_name),
    INDEX idx_state (state),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
);

-- 4. Web应用信息表
CREATE TABLE IF NOT EXISTS web_app_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    url VARCHAR(2048) NOT NULL,
    domain VARCHAR(255),
    ip_address VARCHAR(45),
    port INT DEFAULT 80,
    protocol ENUM('http', 'https') DEFAULT 'http',
    title VARCHAR(1000),
    description TEXT,
    keywords TEXT,
    server VARCHAR(255),
    powered_by VARCHAR(255),
    cms_type VARCHAR(100),
    cms_version VARCHAR(100),
    technologies JSON,
    response_code INT,
    content_length BIGINT,
    content_type VARCHAR(255),
    charset VARCHAR(50),
    language VARCHAR(10),
    last_modified TIMESTAMP NULL,
    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_crawled TIMESTAMP NULL,
    web_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_asset_id (asset_id),
    INDEX idx_domain (domain),
    INDEX idx_ip_port (ip_address, port),
    INDEX idx_cms (cms_type),
    INDEX idx_url (url(255)),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
);

-- 5. 漏洞信息表
CREATE TABLE IF NOT EXISTS vulnerability_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    vuln_type ENUM('network', 'web', 'system', 'config', 'crypto', 'auth') NOT NULL,
    vuln_category VARCHAR(100),
    vuln_name VARCHAR(500) NOT NULL,
    cve_id VARCHAR(20),
    cwe_id VARCHAR(20),
    cvss_score DECIMAL(3,1),
    cvss_vector VARCHAR(255),
    severity ENUM('critical', 'high', 'medium', 'low', 'info') NOT NULL,
    confidence ENUM('confirmed', 'firm', 'tentative') DEFAULT 'tentative',
    description TEXT,
    impact TEXT,
    solution TEXT,
    proof_of_concept TEXT,
    references JSON,
    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_verified TIMESTAMP NULL,
    verified BOOLEAN DEFAULT FALSE,
    false_positive BOOLEAN DEFAULT FALSE,
    exploitable BOOLEAN DEFAULT FALSE,
    exploit_available BOOLEAN DEFAULT FALSE,
    patch_available BOOLEAN DEFAULT FALSE,
    vuln_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_asset_id (asset_id),
    INDEX idx_severity (severity),
    INDEX idx_cve (cve_id),
    INDEX idx_vuln_type (vuln_type),
    INDEX idx_verified (verified),
    INDEX idx_exploitable (exploitable),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
);

-- 6. 威胁情报表
CREATE TABLE IF NOT EXISTS threat_intelligence (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    indicator_type ENUM('ip', 'domain', 'url', 'hash', 'email') NOT NULL,
    indicator_value VARCHAR(2048) NOT NULL,
    threat_type ENUM('malware', 'phishing', 'c2', 'botnet', 'scanner', 'suspicious') NOT NULL,
    confidence_level ENUM('high', 'medium', 'low') DEFAULT 'medium',
    severity ENUM('critical', 'high', 'medium', 'low') DEFAULT 'medium',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(255),
    description TEXT,
    tags JSON,
    geolocation JSON,
    whois_data JSON,
    reputation_score INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    intel_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_asset_id (asset_id),
    INDEX idx_indicator (indicator_type, indicator_value(255)),
    INDEX idx_threat_type (threat_type),
    INDEX idx_confidence (confidence_level),
    INDEX idx_active (is_active),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
);

-- 7. 资产关联关系表
CREATE TABLE IF NOT EXISTS asset_relationships (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    source_asset_id BIGINT NOT NULL,
    target_asset_id BIGINT NOT NULL,
    relationship_type ENUM('subdomain', 'ip_resolution', 'service_on', 'hosted_on', 'redirects_to', 'links_to', 'same_network') NOT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_verified TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_source (source_asset_id),
    INDEX idx_target (target_asset_id),
    INDEX idx_relationship (relationship_type),
    INDEX idx_active (is_active),
    UNIQUE KEY unique_relationship (source_asset_id, target_asset_id, relationship_type),
    FOREIGN KEY (source_asset_id) REFERENCES target_assets(id) ON DELETE CASCADE,
    FOREIGN KEY (target_asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
);

-- 8. 扫描任务表
CREATE TABLE IF NOT EXISTS scan_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_name VARCHAR(255) NOT NULL,
    task_type ENUM('network', 'web', 'comprehensive', 'vulnerability', 'custom') NOT NULL,
    target_specification TEXT NOT NULL,
    scan_config JSON,
    priority ENUM('critical', 'high', 'medium', 'low') DEFAULT 'medium',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    progress INT DEFAULT 0,
    start_time TIMESTAMP NULL,
    end_time TIMESTAMP NULL,
    duration INT,
    results_summary JSON,
    error_message TEXT,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_task_type (task_type),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at)
);

-- 9. 扫描结果表
CREATE TABLE IF NOT EXISTS scan_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id BIGINT NOT NULL,
    asset_id BIGINT,
    result_type ENUM('host_discovery', 'port_scan', 'service_detection', 'vulnerability', 'web_scan') NOT NULL,
    result_data JSON NOT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    scan_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_task_id (task_id),
    INDEX idx_asset_id (asset_id),
    INDEX idx_result_type (result_type),
    INDEX idx_scan_time (scan_time),
    FOREIGN KEY (task_id) REFERENCES scan_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE SET NULL
);

-- 10. AI分析结果表
CREATE TABLE IF NOT EXISTS ai_analysis_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    asset_id BIGINT,
    analysis_type ENUM('vulnerability_assessment', 'risk_analysis', 'attack_path', 'threat_modeling') NOT NULL,
    input_data JSON NOT NULL,
    analysis_result JSON NOT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    risk_score DECIMAL(4,2) DEFAULT 0.00,
    threat_level ENUM('critical', 'high', 'medium', 'low', 'info') DEFAULT 'medium',
    recommendations JSON,
    model_version VARCHAR(50),
    analysis_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_validated BOOLEAN DEFAULT FALSE,
    validation_result JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_asset_id (asset_id),
    INDEX idx_analysis_type (analysis_type),
    INDEX idx_threat_level (threat_level),
    INDEX idx_risk_score (risk_score),
    INDEX idx_analysis_time (analysis_time),
    FOREIGN KEY (asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
);

-- 11. 攻击路径表
CREATE TABLE IF NOT EXISTS attack_paths (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    source_asset_id BIGINT NOT NULL,
    target_asset_id BIGINT NOT NULL,
    path_name VARCHAR(255) NOT NULL,
    attack_vector VARCHAR(255),
    attack_steps JSON NOT NULL,
    difficulty_level ENUM('trivial', 'easy', 'medium', 'hard', 'expert') DEFAULT 'medium',
    success_probability DECIMAL(3,2) DEFAULT 0.00,
    impact_level ENUM('critical', 'high', 'medium', 'low') DEFAULT 'medium',
    prerequisites JSON,
    tools_required JSON,
    estimated_time VARCHAR(50),
    stealth_level ENUM('high', 'medium', 'low') DEFAULT 'medium',
    discovery_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_validated TIMESTAMP NULL,
    is_validated BOOLEAN DEFAULT FALSE,
    validation_result JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_source_asset (source_asset_id),
    INDEX idx_target_asset (target_asset_id),
    INDEX idx_difficulty (difficulty_level),
    INDEX idx_impact (impact_level),
    INDEX idx_validated (is_validated),
    FOREIGN KEY (source_asset_id) REFERENCES target_assets(id) ON DELETE CASCADE,
    FOREIGN KEY (target_asset_id) REFERENCES target_assets(id) ON DELETE CASCADE
);

-- 12. 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(255) NOT NULL UNIQUE,
    config_value TEXT,
    config_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_sensitive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
);

-- 插入默认配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('scan_concurrency', '10', 'integer', '并发扫描任务数量'),
('scan_timeout', '300', 'integer', '扫描超时时间（秒）'),
('ai_analysis_enabled', 'true', 'boolean', '是否启用AI分析'),
('threat_intel_sources', '["virustotal", "shodan", "censys"]', 'json', '威胁情报数据源'),
('vulnerability_scan_enabled', 'true', 'boolean', '是否启用漏洞扫描'),
('auto_validation_enabled', 'false', 'boolean', '是否启用自动验证'),
('data_retention_days', '365', 'integer', '数据保留天数'),
('notification_enabled', 'true', 'boolean', '是否启用通知')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;
