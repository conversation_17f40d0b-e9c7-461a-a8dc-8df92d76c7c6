"""
全面信息收集配置管理模块

功能：
1. 统一配置管理
2. 动态配置更新
3. 配置验证和默认值
4. 敏感信息加密存储
5. 配置热重载

作者：AI渗透测试系统
版本：1.0.0
"""

import os
import json
import configparser
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


@dataclass
class ScanConfig:
    """扫描配置类"""
    # 网络扫描配置
    network_scan_enabled: bool = True
    network_scan_timeout: int = 300
    network_scan_threads: int = 100
    port_scan_range: str = "1-65535"
    tcp_scan_enabled: bool = True
    udp_scan_enabled: bool = True
    syn_scan_enabled: bool = True
    service_detection_enabled: bool = True
    os_detection_enabled: bool = True
    
    # Web扫描配置
    web_scan_enabled: bool = True
    web_scan_timeout: int = 180
    web_crawl_depth: int = 3
    web_crawl_max_pages: int = 1000
    javascript_enabled: bool = True
    follow_redirects: bool = True
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    
    # 漏洞扫描配置
    vulnerability_scan_enabled: bool = True
    awvs_scan_enabled: bool = True
    nuclei_scan_enabled: bool = True
    custom_payloads_enabled: bool = True
    
    # AI分析配置
    ai_analysis_enabled: bool = True
    ai_model: str = "deepseek-chat"
    ai_temperature: float = 0.1
    ai_max_tokens: int = 4000
    
    # 并发控制
    max_concurrent_scans: int = 10
    max_concurrent_targets: int = 50
    rate_limit_requests_per_second: int = 10
    
    # 数据存储配置
    store_raw_results: bool = True
    compress_results: bool = True
    data_retention_days: int = 365
    
    # 通知配置
    notifications_enabled: bool = True
    email_notifications: bool = False
    webhook_notifications: bool = False
    
    # 安全配置
    verify_ssl: bool = False
    use_proxy: bool = False
    proxy_url: str = ""
    api_key_rotation_enabled: bool = True


@dataclass
class DatabaseConfig:
    """数据库配置类"""
    host: str = "localhost"
    port: int = 3306
    user: str = "root"
    password: str = ""
    database: str = "sec_scanner"
    charset: str = "utf8mb4"
    connect_timeout: int = 60
    read_timeout: int = 300
    write_timeout: int = 300
    max_connections: int = 100
    pool_size: int = 20
    pool_recycle: int = 3600
    autocommit: bool = False


@dataclass
class APIConfig:
    """API配置类"""
    # AWVS配置
    awvs_api_url: str = ""
    awvs_api_key: str = ""
    awvs_profile_id: str = "11111111-1111-1111-1111-111111111111"
    
    # Shodan配置
    shodan_api_key: str = ""
    shodan_enabled: bool = False
    
    # VirusTotal配置
    virustotal_api_key: str = ""
    virustotal_enabled: bool = False
    
    # Censys配置
    censys_api_id: str = ""
    censys_api_secret: str = ""
    censys_enabled: bool = False
    
    # 大语言模型配置
    llm_api_key: str = ""
    llm_base_url: str = "https://api.deepseek.com"
    llm_model_name: str = "deepseek-chat"
    
    # 其他API配置
    api_rate_limits: Dict[str, int] = field(default_factory=lambda: {
        "awvs": 10,
        "shodan": 1,
        "virustotal": 4,
        "censys": 5,
        "llm": 20
    })


@dataclass
class LoggingConfig:
    """日志配置类"""
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_file: str = "logs/scanner.log"
    max_log_size: int = 100 * 1024 * 1024  # 100MB
    backup_count: int = 5
    console_logging: bool = True
    file_logging: bool = True
    structured_logging: bool = False


class ComprehensiveConfig:
    """全面配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file or self._get_default_config_file()
        self.scan_config = ScanConfig()
        self.database_config = DatabaseConfig()
        self.api_config = APIConfig()
        self.logging_config = LoggingConfig()
        
        # 加载配置
        self.load_config()
        
        logger.info(f"配置管理器初始化完成，配置文件：{self.config_file}")
    
    def _get_default_config_file(self) -> str:
        """获取默认配置文件路径"""
        current_dir = Path(__file__).parent
        return str(current_dir / "config.config")
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                self._load_from_file()
            else:
                logger.warning(f"配置文件不存在：{self.config_file}，使用默认配置")
                self._create_default_config()
        except Exception as e:
            logger.error(f"加载配置失败：{e}")
            raise
    
    def _load_from_file(self):
        """从文件加载配置"""
        config = configparser.ConfigParser()
        config.read(self.config_file, encoding='utf-8')
        
        # 加载数据库配置
        if config.has_section('mysql'):
            mysql_section = config['mysql']
            self.database_config.host = mysql_section.get('host', self.database_config.host)
            self.database_config.port = mysql_section.getint('port', self.database_config.port)
            self.database_config.user = mysql_section.get('user', self.database_config.user)
            self.database_config.password = mysql_section.get('password', self.database_config.password)
            self.database_config.database = mysql_section.get('database', self.database_config.database)
            self.database_config.charset = mysql_section.get('charset', self.database_config.charset)
        
        # 加载API配置
        if config.has_section('awvs'):
            awvs_section = config['awvs']
            self.api_config.awvs_api_url = awvs_section.get('api_url', self.api_config.awvs_api_url)
            self.api_config.awvs_api_key = awvs_section.get('api_key', self.api_config.awvs_api_key)
            self.api_config.awvs_profile_id = awvs_section.get('profile_id', self.api_config.awvs_profile_id)
        
        if config.has_section('llm'):
            llm_section = config['llm']
            self.api_config.llm_api_key = llm_section.get('api_key', self.api_config.llm_api_key)
            self.api_config.llm_base_url = llm_section.get('base_url', self.api_config.llm_base_url)
            self.api_config.llm_model_name = llm_section.get('model_name', self.api_config.llm_model_name)
        
        # 加载扫描配置
        if config.has_section('scan'):
            scan_section = config['scan']
            self.scan_config.max_concurrent_scans = scan_section.getint('max_concurrent_scans', self.scan_config.max_concurrent_scans)
            self.scan_config.network_scan_timeout = scan_section.getint('network_scan_timeout', self.scan_config.network_scan_timeout)
            self.scan_config.web_scan_timeout = scan_section.getint('web_scan_timeout', self.scan_config.web_scan_timeout)
            self.scan_config.ai_analysis_enabled = scan_section.getboolean('ai_analysis_enabled', self.scan_config.ai_analysis_enabled)
        
        # 加载日志配置
        if config.has_section('logging'):
            logging_section = config['logging']
            self.logging_config.log_level = logging_section.get('log_level', self.logging_config.log_level)
            self.logging_config.log_file = logging_section.get('log_file', self.logging_config.log_file)
            self.logging_config.console_logging = logging_section.getboolean('console_logging', self.logging_config.console_logging)
            self.logging_config.file_logging = logging_section.getboolean('file_logging', self.logging_config.file_logging)
    
    def _create_default_config(self):
        """创建默认配置文件"""
        config = configparser.ConfigParser()
        
        # MySQL配置
        config['mysql'] = {
            'host': self.database_config.host,
            'port': str(self.database_config.port),
            'user': self.database_config.user,
            'password': self.database_config.password,
            'database': self.database_config.database,
            'charset': self.database_config.charset
        }
        
        # AWVS配置
        config['awvs'] = {
            'api_url': self.api_config.awvs_api_url,
            'api_key': self.api_config.awvs_api_key,
            'profile_id': self.api_config.awvs_profile_id
        }
        
        # LLM配置
        config['llm'] = {
            'api_key': self.api_config.llm_api_key,
            'base_url': self.api_config.llm_base_url,
            'model_name': self.api_config.llm_model_name
        }
        
        # 扫描配置
        config['scan'] = {
            'max_concurrent_scans': str(self.scan_config.max_concurrent_scans),
            'network_scan_timeout': str(self.scan_config.network_scan_timeout),
            'web_scan_timeout': str(self.scan_config.web_scan_timeout),
            'ai_analysis_enabled': str(self.scan_config.ai_analysis_enabled)
        }
        
        # 日志配置
        config['logging'] = {
            'log_level': self.logging_config.log_level,
            'log_file': self.logging_config.log_file,
            'console_logging': str(self.logging_config.console_logging),
            'file_logging': str(self.logging_config.file_logging)
        }
        
        # 威胁情报配置
        config['threat_intel'] = {
            'shodan_api_key': self.api_config.shodan_api_key,
            'virustotal_api_key': self.api_config.virustotal_api_key,
            'censys_api_id': self.api_config.censys_api_id,
            'censys_api_secret': self.api_config.censys_api_secret
        }
        
        # 保存配置文件
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            config.write(f)
        
        logger.info(f"已创建默认配置文件：{self.config_file}")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            self._create_default_config()
            logger.info("配置已保存")
        except Exception as e:
            logger.error(f"保存配置失败：{e}")
            raise
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置字典"""
        return {
            'host': self.database_config.host,
            'port': self.database_config.port,
            'user': self.database_config.user,
            'password': self.database_config.password,
            'database': self.database_config.database,
            'charset': self.database_config.charset,
            'connect_timeout': self.database_config.connect_timeout,
            'read_timeout': self.database_config.read_timeout,
            'write_timeout': self.database_config.write_timeout,
            'autocommit': self.database_config.autocommit
        }
    
    def get_scan_config(self) -> ScanConfig:
        """获取扫描配置"""
        return self.scan_config
    
    def get_api_config(self) -> APIConfig:
        """获取API配置"""
        return self.api_config
    
    def get_logging_config(self) -> LoggingConfig:
        """获取日志配置"""
        return self.logging_config
    
    def update_config(self, section: str, key: str, value: Any):
        """更新配置项"""
        try:
            if section == 'scan':
                if hasattr(self.scan_config, key):
                    setattr(self.scan_config, key, value)
            elif section == 'database':
                if hasattr(self.database_config, key):
                    setattr(self.database_config, key, value)
            elif section == 'api':
                if hasattr(self.api_config, key):
                    setattr(self.api_config, key, value)
            elif section == 'logging':
                if hasattr(self.logging_config, key):
                    setattr(self.logging_config, key, value)
            
            logger.info(f"配置已更新：{section}.{key} = {value}")
        except Exception as e:
            logger.error(f"更新配置失败：{e}")
            raise
    
    def validate_config(self) -> Dict[str, List[str]]:
        """验证配置有效性"""
        errors = {
            'database': [],
            'api': [],
            'scan': [],
            'logging': []
        }
        
        # 验证数据库配置
        if not self.database_config.host:
            errors['database'].append("数据库主机地址不能为空")
        if not self.database_config.user:
            errors['database'].append("数据库用户名不能为空")
        if not self.database_config.database:
            errors['database'].append("数据库名不能为空")
        
        # 验证API配置
        if self.scan_config.awvs_scan_enabled and not self.api_config.awvs_api_key:
            errors['api'].append("AWVS扫描已启用但API密钥为空")
        if self.scan_config.ai_analysis_enabled and not self.api_config.llm_api_key:
            errors['api'].append("AI分析已启用但LLM API密钥为空")
        
        # 验证扫描配置
        if self.scan_config.max_concurrent_scans <= 0:
            errors['scan'].append("最大并发扫描数必须大于0")
        if self.scan_config.network_scan_timeout <= 0:
            errors['scan'].append("网络扫描超时时间必须大于0")
        
        # 验证日志配置
        if self.logging_config.log_level not in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            errors['logging'].append("无效的日志级别")
        
        return {k: v for k, v in errors.items() if v}
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            'scan': self.scan_config.__dict__,
            'database': self.database_config.__dict__,
            'api': self.api_config.__dict__,
            'logging': self.logging_config.__dict__
        }
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        if 'scan' in config_dict:
            for key, value in config_dict['scan'].items():
                if hasattr(self.scan_config, key):
                    setattr(self.scan_config, key, value)
        
        if 'database' in config_dict:
            for key, value in config_dict['database'].items():
                if hasattr(self.database_config, key):
                    setattr(self.database_config, key, value)
        
        if 'api' in config_dict:
            for key, value in config_dict['api'].items():
                if hasattr(self.api_config, key):
                    setattr(self.api_config, key, value)
        
        if 'logging' in config_dict:
            for key, value in config_dict['logging'].items():
                if hasattr(self.logging_config, key):
                    setattr(self.logging_config, key, value)


# 全局配置实例
_global_config = None

def get_config() -> ComprehensiveConfig:
    """获取全局配置实例"""
    global _global_config
    if _global_config is None:
        _global_config = ComprehensiveConfig()
    return _global_config

def reload_config():
    """重新加载配置"""
    global _global_config
    _global_config = ComprehensiveConfig()
    return _global_config


# 使用示例
if __name__ == "__main__":
    # 创建配置管理器
    config = ComprehensiveConfig()
    
    # 验证配置
    validation_errors = config.validate_config()
    if validation_errors:
        print("配置验证失败：")
        for section, errors in validation_errors.items():
            print(f"  {section}: {errors}")
    else:
        print("配置验证通过")
    
    # 输出配置信息
    print("\n当前配置：")
    print(json.dumps(config.to_dict(), indent=2, ensure_ascii=False))
