'''
综合信息分析模块

功能：
1. 从多个数据源收集安全扫描信息
    先根据nmap的扫描结果，找出
   - 网络侧：nmap扫描结果
   - Web侧：API扫描、爬虫扫描、AWVS扫描结果
2. 整合分析数据，识别潜在风险点
3. 使用大模型进行深度安全分析
4. 存储分析结果并生成报告

数据流：
1. 从数据库获取原始扫描数据
2. 预处理和标准化数据格式
3. 多维度风险分析
4. 结果存储和可视化
'''


#从nmap_scan_json_result里面获取result
import json
import pymysql
import configparser
import os
import openai  # 或其他大模型SDK
from typing import List, Dict
from openai import OpenAI  # deepseek也用openai接口
from dotenv import load_dotenv

def get_db_connection():
    """获取数据库连接"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'config.config')
    config.read(config_path, encoding='utf-8')
    db_config = {
        'host': config['mysql']['host'],
        'port': int(config['mysql']['port']),
        'user': config['mysql']['user'],
        'password': config['mysql']['password'],
        'database': config['mysql']['database']
    }
    return pymysql.connect(**db_config)


def get_scan_results(scan_type='nmap',):
    """
    从数据库获取扫描结果
    
    参数:
        scan_type: 扫描类型(nmap/api/awvs/spider)
        limit: 返回结果数量限制
    
    返回:
        扫描结果列表
    """
    try:
        db = get_db_connection()
        cursor = db.cursor()
        
        if scan_type == 'nmap':
            query = "SELECT * FROM nmap_scan_json_result ORDER BY scan_time DESC LIMIT %s"
        elif scan_type == 'api':
            query = "SELECT * FROM web_api_results ORDER BY scan_time DESC LIMIT %s"
        elif scan_type == 'awvs':
            query = "SELECT * FROM awvs_scan_results ORDER BY scan_time DESC LIMIT %s"
        elif scan_type == 'spider':
            query = "SELECT * FROM web_spider_results ORDER BY scan_time DESC LIMIT %s"
        else:
            raise ValueError(f"不支持的扫描类型: {scan_type}")
            
        cursor.execute(query, (limit,))
        results = cursor.fetchall()
        
        cursor.close()
        db.close()
        return results
        
    except Exception as e:
        print(f"获取{scan_type}扫描结果时出错: {e}")
        return []


def get_llm_config():
    """从config.config获取LLM配置"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), '..','config', 'config.config')
    config.read(config_path, encoding='utf-8')

    return {
        'api_key': config['llm']['api_key'],
        'base_url': config['llm']['base_url'],
        'model_name': config['llm']['model_name']
    }

def analyze_with_llm(scan_results: List[Dict]) -> Dict:
    llm_config = get_llm_config()
    api_key = llm_config['api_key']
    base_url = llm_config['base_url']
    model_name = llm_config['model_name']

    """使用大模型分析Nmap扫描结果"""
    prompt = """你是一名专业的安全分析师，请基于以下Nmap扫描数据进行深入分析：

1. 分析要求：
- 识别开放端口和服务中的安全风险
- 评估服务配置是否存在漏洞
- 分析发现的CVE漏洞和弱配置
- 按风险等级(高危/中危/低危)分类问题
- 对每个发现提供技术解释和攻击场景
- 给出具体的修复建议

2. 扫描数据：
{scan_data}

3. 输出格式：
### 安全分析报告
#### 关键发现
- [风险等级] 问题描述
  攻击向量: 
  影响程度: 
  修复建议: 

#### 详细分析
按端口和服务组织的详细分析

#### 综合建议
整体安全加固方案"""

    try:
        # 将扫描结果转换为字符串
        scan_data_str = json.dumps(scan_results, indent=2)

        # 调用OpenAI API
        print('api_key',api_key)
        print('base_url',base_url)
        # client = OpenAI(api_key=api_key, base_url=base_url)  # 使用OpenAI客户端
        client = OpenAI(
            api_key="sk-24f69fa80ce44b148c668dd931429720",  # 网页2/8
            base_url="https://api.deepseek.com"  # 网页8
        )

        # response = client.chat.completions.create(
        #     model="deepseek-chat",  # 网页8
        #     messages=[
        #         {"role": "user", "content": "请用DeepSeek模型生成一首中文短诗"}
        #     ],
        #     temperature=0.5,
        #     max_tokens=200,
        #     stream=True  # 网页8支持流式响应
        # )
        # print('response',response)
        # # 流式输出处理（网页8）
        # for chunk in response:
        #     print(chunk.choices[0].delta.content, end="", flush=True)

        # 使用预定义的prompt模板，填入扫描结果
        formatted_prompt = prompt.format(scan_data=scan_data_str)

        print(1)

        response = client.chat.completions.create(
            # model="deepseek-v3",  # 阿里云用的是模型名称，deepseek官网用的是deepseek-chat
            model="deepseek-chat",
            messages=[
                {"role": "system",
                 "content": "You are a cybersecurity expert specialized in vulnerability analysis and exploitation."},
                {"role": "user", "content": formatted_prompt}
            ],
            temperature=0
        )
        print(formatted_prompt)
        print(response)
        # for chunk in response:
        #     print(chunk.choices[0].delta.content, end="", flush=True)
        analysis = response.choices[0].message.content
        print(analysis)

        return {
            "status": "success",
            "analysis": analysis
        }
    except Exception as e:
        print(f"Error during LLM analysis: {e}")


def save_analysis_result(target: str, analysis: Dict, scan_type: str):
    """
    保存分析结果到数据库
    
    参数:
        target: 目标(IP或域名)
        analysis: 分析结果
        scan_type: 扫描类型
    """
    try:
        db = get_db_connection()
        cursor = db.cursor()
        
        # 创建分析结果表(如果不存在)
        create_table = """
        CREATE TABLE IF NOT EXISTS security_analysis_results (
            id INT AUTO_INCREMENT PRIMARY KEY,
            target VARCHAR(255) NOT NULL,
            scan_type VARCHAR(50) NOT NULL,
            analysis_result JSON,
            risk_level VARCHAR(20),
            analysis_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY target_scan_type (target, scan_type)
        )"""
        cursor.execute(create_table)
        
        # 计算风险等级
        risk_level = calculate_risk_level(analysis)
        
        # 插入或更新结果
        query = """
        INSERT INTO security_analysis_results 
        (target, scan_type, analysis_result, risk_level)
        VALUES (%s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE 
            analysis_result = VALUES(analysis_result),
            risk_level = VALUES(risk_level),
            analysis_time = CURRENT_TIMESTAMP
        """
        cursor.execute(query, (target, scan_type, json.dumps(analysis), risk_level))
        db.commit()
        cursor.close()
        db.close()
        print(f"成功保存{scan_type}分析结果: {target}")
    except Exception as e:
        print(f"保存分析结果时出错: {e}")


def main():
    # 获取所有扫描结果
    scan_result = get_nmap_scan_results()
    print('获取nmap扫描结果',scan_result)

    ip = scan_result[1]
    print(f"分析IP: {ip}的扫描结果...")

    # 使用大模型分析
    print(scan_result[2])
    analysis = analyze_with_llm(scan_result[2])

    if analysis['status'] == 'success':
        # 保存分析结果
        save_analysis_result(ip, analysis)
        print(f"IP {ip} 分析完成并保存结果")
    else:
        print(f"IP {ip} 分析失败: {analysis['details']}")


main()