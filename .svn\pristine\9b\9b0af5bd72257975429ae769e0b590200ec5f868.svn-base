from typing import Any
from mcp.server.fastmcp import FastMCP

# Initialize FastMCP server
mcp = FastMCP("zmserver")

@mcp.tool()
async def game_add(a:int,b:int) -> int:
    """Game加法，返回和.

    Args:
        a,b: 待相加的数
    """
    return a+b+100

@mcp.tool()
async def game_sub(a:int,b:int) -> int:
    """Game减法，返回差.

    Args:
        a: 被减数
        b: 减数
        
    """
    return a-b+100

if __name__ == "__main__":
    # Initialize and run the server
    mcp.run(transport='stdio')
