"""
数据库扫描器测试脚本

功能：
1. 测试数据库扫描功能
2. 验证漏洞检测能力
3. 测试报告生成
4. 性能测试

作者：AI渗透测试系统
版本：1.0.0
"""

import os
import sys
import json
import time
from typing import List, Dict, Any

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from info_collector.database.database_scanner import DatabaseScanner, DatabaseVulnerabilityScanner
from info_collector.database.database_integration import DatabaseScanIntegrator
from info_collector.database.database_config import DatabaseScanConfig, get_default_config


class DatabaseScannerTester:
    """数据库扫描器测试类"""
    
    def __init__(self):
        self.scanner = DatabaseScanner()
        self.vuln_scanner = DatabaseVulnerabilityScanner()
        self.integrator = DatabaseScanIntegrator()
        
        # 测试目标
        self.test_targets = [
            "127.0.0.1",      # 本地测试
            "***********",   # 网关
            "*******",       # 公网DNS
            "localhost"       # 本地主机
        ]
        
        # 测试端口
        self.test_ports = [3306, 5432, 27017, 6379, 9200]
    
    def test_basic_scanning(self):
        """测试基础扫描功能"""
        print("=" * 60)
        print("测试基础数据库扫描功能")
        print("=" * 60)
        
        for target in self.test_targets:
            print(f"\n测试目标：{target}")
            try:
                start_time = time.time()
                results = self.scanner.scan_database_services(target, self.test_ports)
                scan_time = time.time() - start_time
                
                print(f"扫描耗时：{scan_time:.2f}秒")
                print(f"发现数据库：{len(results.get('discovered_databases', []))}个")
                print(f"发现漏洞：{len(results.get('vulnerabilities', []))}个")
                
                # 显示发现的数据库
                for db in results.get('discovered_databases', []):
                    print(f"  - {db.get('type')} on {db.get('host')}:{db.get('port')}")
                    if db.get('version'):
                        print(f"    版本：{db.get('version')}")
                    
                    auth_info = db.get('authentication', {})
                    if auth_info.get('anonymous_access'):
                        print(f"    ⚠️  允许匿名访问")
                    if auth_info.get('weak_credentials'):
                        print(f"    ⚠️  发现弱密码：{len(auth_info['weak_credentials'])}个")
                
            except Exception as e:
                print(f"扫描失败：{e}")
    
    def test_vulnerability_detection(self):
        """测试漏洞检测功能"""
        print("\n" + "=" * 60)
        print("测试漏洞检测功能")
        print("=" * 60)
        
        # 模拟数据库信息进行漏洞检测测试
        test_db_info = {
            'type': 'MySQL',
            'host': '127.0.0.1',
            'port': 3306,
            'version': '5.7.10',  # 已知有漏洞的版本
            'authentication': {
                'weak_credentials': [{'username': 'root', 'password': ''}]
            },
            'configuration': {
                'ssl_enabled': False,
                'general_log': 'OFF'
            }
        }
        
        try:
            vulnerabilities = self.scanner._detect_database_vulnerabilities('127.0.0.1', test_db_info)
            
            print(f"检测到漏洞：{len(vulnerabilities)}个")
            for vuln in vulnerabilities:
                print(f"  - [{vuln.get('severity', 'unknown').upper()}] {vuln.get('description', 'Unknown')}")
                if vuln.get('cve'):
                    print(f"    CVE：{vuln['cve']}")
                if vuln.get('recommendation'):
                    print(f"    建议：{vuln['recommendation']}")
                print()
        
        except Exception as e:
            print(f"漏洞检测测试失败：{e}")
    
    def test_integration_features(self):
        """测试集成功能"""
        print("\n" + "=" * 60)
        print("测试集成功能")
        print("=" * 60)
        
        # 测试配置
        config = get_default_config()
        config.save_raw_results = False  # 测试时不保存文件
        config.generate_report = True
        
        integrator = DatabaseScanIntegrator(config)
        
        try:
            # 测试单目标扫描
            print("测试单目标扫描...")
            results = integrator.scan_target("127.0.0.1", [3306, 5432])
            
            print(f"扫描耗时：{results.get('scan_duration', 0):.2f}秒")
            
            # 测试统计功能
            stats = integrator.get_database_statistics(results)
            print(f"统计信息：{json.dumps(stats, indent=2, ensure_ascii=False)}")
            
            # 测试报告生成
            if results.get('report'):
                print("报告生成成功")
                summary = results['report']['summary']
                print(f"报告摘要：发现{summary['total_databases_found']}个数据库，{summary['total_vulnerabilities']}个漏洞")
        
        except Exception as e:
            print(f"集成功能测试失败：{e}")
    
    def test_multiple_targets(self):
        """测试多目标扫描"""
        print("\n" + "=" * 60)
        print("测试多目标扫描")
        print("=" * 60)
        
        test_targets = ["127.0.0.1", "localhost"]
        
        try:
            start_time = time.time()
            results = self.integrator.scan_multiple_targets(test_targets)
            total_time = time.time() - start_time
            
            print(f"批量扫描耗时：{total_time:.2f}秒")
            print(f"扫描目标：{results['summary']['total_targets']}个")
            print(f"成功扫描：{results['summary']['successful_scans']}个")
            print(f"失败扫描：{results['summary']['failed_scans']}个")
            print(f"发现数据库：{results['summary']['total_databases_found']}个")
            print(f"发现漏洞：{results['summary']['total_vulnerabilities']}个")
        
        except Exception as e:
            print(f"多目标扫描测试失败：{e}")
    
    def test_specific_database_types(self):
        """测试特定数据库类型的扫描"""
        print("\n" + "=" * 60)
        print("测试特定数据库类型扫描")
        print("=" * 60)
        
        # 测试不同数据库类型的验证功能
        database_tests = [
            ('MySQL', 3306),
            ('PostgreSQL', 5432),
            ('MongoDB', 27017),
            ('Redis', 6379),
            ('Elasticsearch', 9200)
        ]
        
        for db_type, port in database_tests:
            print(f"\n测试{db_type}识别...")
            try:
                # 测试端口检查
                port_open = self.scanner._check_port_open('127.0.0.1', port, timeout=2)
                print(f"  端口{port}状态：{'开放' if port_open else '关闭'}")
                
                if port_open:
                    # 测试数据库类型识别
                    identified_type = self.scanner._identify_database_type('127.0.0.1', port)
                    print(f"  识别类型：{identified_type or '未识别'}")
                    
                    if identified_type == db_type:
                        print(f"  ✅ {db_type}识别成功")
                    elif identified_type:
                        print(f"  ⚠️  识别为{identified_type}，期望{db_type}")
                    else:
                        print(f"  ❌ 未能识别{db_type}")
            
            except Exception as e:
                print(f"  测试{db_type}时出错：{e}")
    
    def test_performance(self):
        """测试性能"""
        print("\n" + "=" * 60)
        print("性能测试")
        print("=" * 60)
        
        # 测试大量端口扫描的性能
        large_port_list = list(range(3300, 3400))  # 100个端口
        
        print(f"测试扫描{len(large_port_list)}个端口的性能...")
        
        try:
            start_time = time.time()
            results = self.scanner.scan_database_services('127.0.0.1', large_port_list)
            scan_time = time.time() - start_time
            
            print(f"扫描{len(large_port_list)}个端口耗时：{scan_time:.2f}秒")
            print(f"平均每端口耗时：{scan_time/len(large_port_list)*1000:.2f}毫秒")
            print(f"发现数据库：{len(results.get('discovered_databases', []))}个")
        
        except Exception as e:
            print(f"性能测试失败：{e}")
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n" + "=" * 60)
        print("测试错误处理")
        print("=" * 60)
        
        # 测试无效目标
        invalid_targets = [
            "999.999.999.999",  # 无效IP
            "nonexistent.domain.invalid",  # 无效域名
            "",  # 空字符串
        ]
        
        for target in invalid_targets:
            print(f"\n测试无效目标：{target}")
            try:
                results = self.scanner.scan_database_services(target, [3306])
                print(f"  结果：{len(results.get('discovered_databases', []))}个数据库")
                if results.get('error'):
                    print(f"  错误：{results['error']}")
            except Exception as e:
                print(f"  异常：{e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始数据库扫描器测试")
        print("测试时间：", time.strftime('%Y-%m-%d %H:%M:%S'))
        
        test_methods = [
            self.test_basic_scanning,
            self.test_vulnerability_detection,
            self.test_integration_features,
            self.test_multiple_targets,
            self.test_specific_database_types,
            self.test_performance,
            self.test_error_handling
        ]
        
        for i, test_method in enumerate(test_methods, 1):
            try:
                print(f"\n[{i}/{len(test_methods)}] 执行测试：{test_method.__name__}")
                test_method()
            except Exception as e:
                print(f"测试{test_method.__name__}失败：{e}")
        
        print("\n" + "=" * 60)
        print("所有测试完成")
        print("=" * 60)


def main():
    """主函数"""
    tester = DatabaseScannerTester()
    
    # 可以选择运行特定测试或所有测试
    import sys
    
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        if hasattr(tester, test_name):
            print(f"运行单个测试：{test_name}")
            getattr(tester, test_name)()
        else:
            print(f"未找到测试方法：{test_name}")
            print("可用的测试方法：")
            for method in dir(tester):
                if method.startswith('test_'):
                    print(f"  - {method}")
    else:
        # 运行所有测试
        tester.run_all_tests()


if __name__ == "__main__":
    main()
