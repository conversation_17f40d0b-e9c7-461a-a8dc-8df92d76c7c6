CREATE TABLE IF NOT EXISTS ip_segment_task (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_segment VARCHAR(45) NOT NULL UNIQUE,  -- 支持IPv4和IPv6
    scan_type ENUM('nmap') NOT NULL,  -- 当前支持的扫描类型
    status ENUM('todo', 'in_progress', 'done') NOT NULL,
    domain_status ENUM('todo', 'in_progress', 'done') NOT NULL DEFAULT 'todo',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);