"""
数据库存储管理器

功能：
1. 统一管理所有信息收集模块的数据存储
2. 确保数据完整性和一致性
3. 提供标准化的存储接口
4. 支持批量存储和事务处理
5. 数据去重和更新策略

作者：AI渗透测试系统
版本：1.0.0
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
import logging
import pymysql
import configparser
from contextlib import contextmanager

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

logger = logging.getLogger(__name__)


class DatabaseStorageManager:
    """数据库存储管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化存储管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path or self._get_default_config_path()
        self.db_config = self._load_db_config()
        
        logger.info("数据库存储管理器初始化完成")
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        return os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
    
    def _load_db_config(self) -> Dict[str, Any]:
        """加载数据库配置"""
        try:
            config = configparser.ConfigParser()
            config.read(self.config_path, encoding='utf-8')
            
            return {
                'host': config['mysql']['host'],
                'port': int(config['mysql']['port']),
                'user': config['mysql']['user'],
                'password': config['mysql']['password'],
                'database': config['mysql']['database'],
                'charset': config.get('mysql', 'charset', fallback='utf8mb4'),
                'connect_timeout': 60,
                'read_timeout': 300,
                'write_timeout': 300,
                'autocommit': False
            }
        except Exception as e:
            logger.error(f"加载数据库配置失败：{e}")
            raise
    
    @contextmanager
    def get_db_connection(self):
        """获取数据库连接上下文管理器"""
        connection = None
        try:
            connection = pymysql.connect(**self.db_config)
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"数据库连接错误：{e}")
            raise
        finally:
            if connection:
                connection.close()
    
    def store_target_asset(self, asset_data: Dict[str, Any]) -> Optional[int]:
        """
        存储目标资产信息
        
        Args:
            asset_data: 资产数据
            
        Returns:
            Optional[int]: 资产ID，失败返回None
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 检查资产是否已存在
                check_query = """
                SELECT id FROM target_assets 
                WHERE asset_type = %s AND asset_value = %s
                """
                cursor.execute(check_query, (asset_data['asset_type'], asset_data['asset_value']))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有资产
                    asset_id = existing[0]
                    update_query = """
                    UPDATE target_assets SET
                        asset_name = %s,
                        description = %s,
                        business_importance = %s,
                        last_scan_time = %s,
                        status = %s,
                        confidence_score = %s,
                        metadata = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    cursor.execute(update_query, (
                        asset_data.get('asset_name'),
                        asset_data.get('description'),
                        asset_data.get('business_importance', 'medium'),
                        asset_data.get('last_scan_time'),
                        asset_data.get('status', 'active'),
                        asset_data.get('confidence_score', 0.0),
                        json.dumps(asset_data.get('metadata', {}), ensure_ascii=False),
                        asset_id
                    ))
                else:
                    # 插入新资产
                    insert_query = """
                    INSERT INTO target_assets (
                        asset_type, asset_value, asset_name, description,
                        business_importance, last_scan_time, status,
                        confidence_score, metadata
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_query, (
                        asset_data['asset_type'],
                        asset_data['asset_value'],
                        asset_data.get('asset_name'),
                        asset_data.get('description'),
                        asset_data.get('business_importance', 'medium'),
                        asset_data.get('last_scan_time'),
                        asset_data.get('status', 'active'),
                        asset_data.get('confidence_score', 0.0),
                        json.dumps(asset_data.get('metadata', {}), ensure_ascii=False)
                    ))
                    asset_id = cursor.lastrowid
                
                conn.commit()
                cursor.close()
                
                logger.info(f"成功存储资产：{asset_data['asset_value']} (ID: {asset_id})")
                return asset_id
                
        except Exception as e:
            logger.error(f"存储目标资产失败：{e}")
            return None
    
    def store_network_info(self, asset_id: int, network_data: Dict[str, Any]) -> bool:
        """
        存储网络信息
        
        Args:
            asset_id: 资产ID
            network_data: 网络数据
            
        Returns:
            bool: 存储是否成功
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在相同IP的记录
                check_query = """
                SELECT id FROM network_info 
                WHERE asset_id = %s AND ip_address = %s
                """
                cursor.execute(check_query, (asset_id, network_data['ip_address']))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录
                    update_query = """
                    UPDATE network_info SET
                        mac_address = %s, hostname = %s, os_type = %s,
                        os_version = %s, os_accuracy = %s, network_segment = %s,
                        gateway = %s, response_time = %s, ttl = %s,
                        last_seen = %s, scan_method = %s, scan_data = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    cursor.execute(update_query, (
                        network_data.get('mac_address'),
                        network_data.get('hostname'),
                        network_data.get('os_type'),
                        network_data.get('os_version'),
                        network_data.get('os_accuracy', 0),
                        network_data.get('network_segment'),
                        network_data.get('gateway'),
                        network_data.get('response_time'),
                        network_data.get('ttl'),
                        network_data.get('last_seen', datetime.now()),
                        network_data.get('scan_method'),
                        json.dumps(network_data.get('scan_data', {}), ensure_ascii=False),
                        existing[0]
                    ))
                else:
                    # 插入新记录
                    insert_query = """
                    INSERT INTO network_info (
                        asset_id, ip_address, mac_address, hostname, os_type,
                        os_version, os_accuracy, network_segment, gateway,
                        response_time, ttl, last_seen, scan_method, scan_data
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_query, (
                        asset_id,
                        network_data['ip_address'],
                        network_data.get('mac_address'),
                        network_data.get('hostname'),
                        network_data.get('os_type'),
                        network_data.get('os_version'),
                        network_data.get('os_accuracy', 0),
                        network_data.get('network_segment'),
                        network_data.get('gateway'),
                        network_data.get('response_time'),
                        network_data.get('ttl'),
                        network_data.get('last_seen', datetime.now()),
                        network_data.get('scan_method'),
                        json.dumps(network_data.get('scan_data', {}), ensure_ascii=False)
                    ))
                
                conn.commit()
                cursor.close()
                
                logger.info(f"成功存储网络信息：{network_data['ip_address']}")
                return True
                
        except Exception as e:
            logger.error(f"存储网络信息失败：{e}")
            return False
    
    def store_service_info(self, asset_id: int, service_data: Dict[str, Any]) -> bool:
        """
        存储服务信息
        
        Args:
            asset_id: 资产ID
            service_data: 服务数据
            
        Returns:
            bool: 存储是否成功
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在相同服务的记录
                check_query = """
                SELECT id FROM service_info 
                WHERE asset_id = %s AND ip_address = %s AND port = %s AND protocol = %s
                """
                cursor.execute(check_query, (
                    asset_id, 
                    service_data['ip_address'], 
                    service_data['port'], 
                    service_data['protocol']
                ))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录
                    update_query = """
                    UPDATE service_info SET
                        service_name = %s, service_version = %s, service_product = %s,
                        service_banner = %s, service_extrainfo = %s, state = %s,
                        confidence_score = %s, last_verified = %s, service_data = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    cursor.execute(update_query, (
                        service_data.get('service_name'),
                        service_data.get('service_version'),
                        service_data.get('service_product'),
                        service_data.get('service_banner'),
                        service_data.get('service_extrainfo'),
                        service_data.get('state', 'open'),
                        service_data.get('confidence_score', 0.0),
                        service_data.get('last_verified', datetime.now()),
                        json.dumps(service_data.get('service_data', {}), ensure_ascii=False),
                        existing[0]
                    ))
                else:
                    # 插入新记录
                    insert_query = """
                    INSERT INTO service_info (
                        asset_id, ip_address, port, protocol, service_name,
                        service_version, service_product, service_banner,
                        service_extrainfo, state, confidence_score,
                        discovery_time, last_verified, service_data
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_query, (
                        asset_id,
                        service_data['ip_address'],
                        service_data['port'],
                        service_data['protocol'],
                        service_data.get('service_name'),
                        service_data.get('service_version'),
                        service_data.get('service_product'),
                        service_data.get('service_banner'),
                        service_data.get('service_extrainfo'),
                        service_data.get('state', 'open'),
                        service_data.get('confidence_score', 0.0),
                        service_data.get('discovery_time', datetime.now()),
                        service_data.get('last_verified', datetime.now()),
                        json.dumps(service_data.get('service_data', {}), ensure_ascii=False)
                    ))
                
                conn.commit()
                cursor.close()
                
                logger.info(f"成功存储服务信息：{service_data['ip_address']}:{service_data['port']}")
                return True
                
        except Exception as e:
            logger.error(f"存储服务信息失败：{e}")
            return False
    
    def store_web_app_info(self, asset_id: int, web_data: Dict[str, Any]) -> bool:
        """
        存储Web应用信息
        
        Args:
            asset_id: 资产ID
            web_data: Web应用数据
            
        Returns:
            bool: 存储是否成功
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在相同URL的记录
                check_query = """
                SELECT id FROM web_app_info 
                WHERE asset_id = %s AND url = %s
                """
                cursor.execute(check_query, (asset_id, web_data['url']))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录
                    update_query = """
                    UPDATE web_app_info SET
                        domain = %s, ip_address = %s, port = %s, protocol = %s,
                        title = %s, description = %s, keywords = %s, server = %s,
                        powered_by = %s, cms_type = %s, cms_version = %s,
                        technologies = %s, response_code = %s, content_length = %s,
                        content_type = %s, charset = %s, language = %s,
                        last_modified = %s, last_crawled = %s, web_data = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    cursor.execute(update_query, (
                        web_data.get('domain'),
                        web_data.get('ip_address'),
                        web_data.get('port', 80),
                        web_data.get('protocol', 'http'),
                        web_data.get('title'),
                        web_data.get('description'),
                        web_data.get('keywords'),
                        web_data.get('server'),
                        web_data.get('powered_by'),
                        web_data.get('cms_type'),
                        web_data.get('cms_version'),
                        json.dumps(web_data.get('technologies', []), ensure_ascii=False),
                        web_data.get('response_code'),
                        web_data.get('content_length'),
                        web_data.get('content_type'),
                        web_data.get('charset'),
                        web_data.get('language'),
                        web_data.get('last_modified'),
                        web_data.get('last_crawled', datetime.now()),
                        json.dumps(web_data.get('web_data', {}), ensure_ascii=False),
                        existing[0]
                    ))
                else:
                    # 插入新记录
                    insert_query = """
                    INSERT INTO web_app_info (
                        asset_id, url, domain, ip_address, port, protocol,
                        title, description, keywords, server, powered_by,
                        cms_type, cms_version, technologies, response_code,
                        content_length, content_type, charset, language,
                        last_modified, discovery_time, last_crawled, web_data
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_query, (
                        asset_id,
                        web_data['url'],
                        web_data.get('domain'),
                        web_data.get('ip_address'),
                        web_data.get('port', 80),
                        web_data.get('protocol', 'http'),
                        web_data.get('title'),
                        web_data.get('description'),
                        web_data.get('keywords'),
                        web_data.get('server'),
                        web_data.get('powered_by'),
                        web_data.get('cms_type'),
                        web_data.get('cms_version'),
                        json.dumps(web_data.get('technologies', []), ensure_ascii=False),
                        web_data.get('response_code'),
                        web_data.get('content_length'),
                        web_data.get('content_type'),
                        web_data.get('charset'),
                        web_data.get('language'),
                        web_data.get('last_modified'),
                        web_data.get('discovery_time', datetime.now()),
                        web_data.get('last_crawled', datetime.now()),
                        json.dumps(web_data.get('web_data', {}), ensure_ascii=False)
                    ))
                
                conn.commit()
                cursor.close()
                
                logger.info(f"成功存储Web应用信息：{web_data['url']}")
                return True
                
        except Exception as e:
            logger.error(f"存储Web应用信息失败：{e}")
            return False
