#用于将ip分为不同的c段，然后插入扫描任务供扫描器扫描
#要求排除局域网ip
#要求支持IPv6
#任务切割完成后插入到数据库中


import os
import ipaddress
import pymysql
import configparser
import time
import traceback
from typing import Optional

def get_db_connection():
    """
    获取数据库连接，包含重连机制

    Returns:
        pymysql.connections.Connection: 数据库连接对象
    """
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.config')
    config = configparser.ConfigParser()
    config.read(config_path, encoding='utf-8')

    if not config.has_section('mysql'):
        raise ValueError("MySQL section not found in config file")

    db_config = {
        'host': config['mysql']['host'],
        'port': int(config['mysql']['port']),
        'user': config['mysql']['user'],
        'password': config['mysql']['password'],
        'database': config['mysql']['database'],
        'connect_timeout': 60,
        'read_timeout': 300,
        'write_timeout': 300,
        'autocommit': False,
        'charset': 'utf8mb4'
    }

    max_retries = 3
    for attempt in range(max_retries):
        try:
            connection = pymysql.connect(**db_config)
            # 测试连接
            connection.ping(reconnect=True)
            return connection
        except Exception as e:
            print(f"数据库连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
            else:
                raise

def execute_with_retry(query, params=None, max_retries=3):
    """
    执行SQL查询，包含重试机制

    Args:
        query (str): SQL查询语句
        params (tuple): 查询参数
        max_retries (int): 最大重试次数

    Returns:
        tuple: 查询结果
    """
    for attempt in range(max_retries):
        connection = None
        cursor = None
        try:
            connection = get_db_connection()
            cursor = connection.cursor()

            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            # 如果是SELECT查询，获取结果
            if query.strip().upper().startswith('SELECT'):
                result = cursor.fetchall()
            else:
                result = cursor.rowcount
                connection.commit()

            return result

        except (pymysql.err.OperationalError, pymysql.err.InterfaceError) as e:
            print(f"数据库操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
            else:
                raise
        except Exception as e:
            print(f"执行SQL时发生错误: {e}")
            traceback.print_exc()
            raise
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

def insert_scan_task(ip_range):
    """
    插入扫描任务到数据库，使用重试机制

    Args:
        ip_range (str): IP地址范围
    """
    print('start insert:', ip_range)

    try:
        network = ipaddress.ip_network(ip_range)
        # 判断整个网段是否是局域网网段
        if not network.is_private:
            query = """
            INSERT INTO ip_segment_task (ip_segment, scan_type, status)
            VALUES (%s, %s, %s)
            ON DUPLICATE KEY UPDATE status = VALUES(status)
            """
            print('执行SQL:', query)
            execute_with_retry(query, (str(network), 'nmap', 'todo'))
            print(f'成功插入扫描任务: {ip_range}')
        else:
            print('跳过局域网网段:', ip_range)

    except ValueError as e:
        print(f"无效的IP范围: {e}")
    except Exception as e:
        print(f"插入扫描任务时发生错误: {e}")
        traceback.print_exc()

def get_last_progress(task_type):
    """
    从数据库中获取上次扫描的进度

    Args:
        task_type (str): 任务类型

    Returns:
        str: 上次扫描的进度，如果没有则返回None
    """
    try:
        query = "SELECT task_tmp FROM task_tmp WHERE task_type = %s"
        print('查询上次进度:', query)

        result = execute_with_retry(query, (task_type,))
        print('查询结果:', result)

        return result[0][0] if result and len(result) > 0 else None

    except Exception as e:
        print(f"获取上次进度时发生错误: {e}")
        traceback.print_exc()
        return None

def update_progress(task_type, ip_range):
    """
    更新数据库中的扫描进度

    Args:
        task_type (str): 任务类型
        ip_range (str): IP地址范围
    """
    try:
        query = "INSERT INTO task_tmp (task_type, task_tmp) VALUES (%s, %s) ON DUPLICATE KEY UPDATE task_tmp = %s"
        execute_with_retry(query, (task_type, ip_range, ip_range))
        print(f'成功更新进度: {task_type} -> {ip_range}')

    except Exception as e:
        print(f"更新进度时发生错误: {e}")
        traceback.print_exc()

def generate_ip_ranges():
    """
    生成所有A段、B段和C段的IP范围
    """
    last_ip_range = get_last_progress('ipv4')
    print('上次IPv4进度:', last_ip_range)
    skip = True if last_ip_range else False

    processed_count = 0  # 实际处理的段数
    skipped_count = 0    # 跳过的段数（局域网段 + 已处理段）
    total_scanned = 0    # 总扫描位置计数

    # IPv4总段数：256*256*256 = 16,777,216
    # 大约有1800万个局域网段，所以大概有1500万个公网段需要处理
    print('开始处理IPv4段生成...')

    for i in range(0, 256):
        for j in range(0, 256):
            for k in range(0, 256):
                ip_range = f"{i}.{j}.{k}.0/24"
                total_scanned += 1

                # 判断是否为局域网网段
                try:
                    network = ipaddress.ip_network(ip_range)
                    if network.is_private:
                        skipped_count += 1
                        continue  # 跳过局域网网段
                except ValueError as e:
                    print(f"无效的IP范围: {e}")
                    skipped_count += 1
                    continue

                if skip and ip_range <= last_ip_range:
                    skipped_count += 1
                    continue  # 跳过已扫描的部分
                skip = False

                # 显示更清晰的进度信息
                print(f'处理IPv4段: {ip_range}')
                print(f'  - 已处理: {processed_count} 段')
                print(f'  - 已跳过: {skipped_count} 段 (局域网段 + 已处理段)')
                print(f'  - 扫描位置: {total_scanned}/16777216 (总IPv4段)')
                print(f'  - 处理进度: {(processed_count/(processed_count+skipped_count+1)*100):.2f}%')

                insert_scan_task(ip_range)
                processed_count += 1

                # 每处理100个IP段就更新一次进度，减少数据库写入频率
                if processed_count % 100 == 0:
                    update_progress('ipv4', ip_range)
                    print(f'✓ 已处理 {processed_count} 个IPv4段，当前进度已保存')

                # 添加短暂延迟，避免过度占用数据库连接
                if processed_count % 50 == 0:
                    time.sleep(0.1)

    # 最后更新一次进度
    if processed_count > 0:
        update_progress('ipv4', ip_range)
        print(f'IPv4范围生成完成！')
        print(f'  - 总共处理: {processed_count} 个段')
        print(f'  - 总共跳过: {skipped_count} 个段')
        print(f'  - 扫描完成: {total_scanned}/16777216 个IPv4段')

def generate_ipv6_ranges():
    """
    生成常见的IPv6范围
    """
    last_ip_range = get_last_progress('ipv6')
    print('上次IPv6进度:', last_ip_range)
    skip = True if last_ip_range else False

    processed_count = 0
    total_count = 0

    try:
        global_unicast = ipaddress.ip_network('2000::/3')
        subnets = list(global_unicast.subnets(new_prefix=64))
        total_count = len(subnets)
        print(f'总共需要处理 {total_count} 个IPv6段')

        for subnet in subnets:
            ipv6_range = str(subnet)

            # 判断是否为局域网网段
            try:
                network = ipaddress.ip_network(ipv6_range)
                if network.is_private:
                    continue  # 跳过局域网网段
            except ValueError as e:
                print(f"无效的IPv6范围: {e}")
                continue

            if skip and ipv6_range <= last_ip_range:
                continue  # 跳过已扫描的部分
            skip = False

            print(f'处理IPv6范围: {ipv6_range} (进度: {processed_count}/{total_count})')
            insert_scan_task(ipv6_range)
            processed_count += 1

            # 每处理50个IPv6段就更新一次进度
            if processed_count % 50 == 0:
                update_progress('ipv6', ipv6_range)
                print(f'已处理 {processed_count} 个IPv6段，当前进度已保存')

            # 添加短暂延迟
            if processed_count % 25 == 0:
                time.sleep(0.1)

        # 最后更新一次进度
        if processed_count > 0:
            update_progress('ipv6', ipv6_range)
            print(f'IPv6范围生成完成，总共处理了 {processed_count} 个段')

    except Exception as e:
        print(f"生成IPv6范围时发生错误: {e}")
        traceback.print_exc()


# 使用示例
def main():
    generate_ip_ranges()
    generate_ipv6_ranges()
# if __name__ == "__main__":
#     # 生成所有A段、B段和C段的IP范围
#     generate_ip_ranges()
    
#     # 生成IPv6范围
#     generate_ipv6_ranges()
main()