# 本目录发布一些常用工具。
## 1. llmsysadmin.py
   使用deepseek来辅助做系统管理员。详细说明见<BR>
   https://mp.weixin.qq.com/s/tECTJftF9QdVub5OfyHWuQ
   
## 2. autoexploit.py
   使用deepseek辅助自动化测试，详细说明见<BR>
   https://mp.weixin.qq.com/s/NpowPy_JLPV8rPFEwYKxzQ?token=2131257808&lang=zh_CN

## 3. broweruseforweb.py
   使用browser-use和deepseek,实现对靶机的自动化操作,详细说明见<BR>
   https://mp.weixin.qq.com/s/YLnMMZfmXhZrdpqyzcqR0g

## 4.interactivewebpentest.py
   可以交互式的web渗透测试工具，人可以直接指挥大模型工作
   https://mp.weixin.qq.com/s/gRF3woxBW_BD55SsHtbAkw
## 5.pentestlearningagent.py
   可以用于训练的辅助渗透测试代理
   https://mp.weixin.qq.com/s/sLMecmQd9nQVYEz2wQRfmw


## 使用方法
1. 申请deepseek的api key,在运行目录下创建.env文件，内容如下，将api_key换成自己的就可以了
<code>
#config for deepseek
api_key='sk-xxx'
base_url='https://api.deepseek.com/v1'
modelname='deepseek-chat'
</code>
2. 下载文件到本地，运行 python XXX.py <br>
3. 如果不能运行，缺什么库， pip install安装即可。<br>



更多信息，请关注公众号 AI与安全
![image](https://github.com/user-attachments/assets/1900e98a-6aab-46b8-8563-8c6b83b709ff)
