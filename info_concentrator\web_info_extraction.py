import requests
import json

# 配置参数（需替换为实际值）
API_KEY = "sk-24f69fa80ce44b148c668dd931429720"  # 请替换为实际的 API 密钥
API_URL = "https://api.deepseek.com/v1/chat/completions"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

payload = {
    "model": "deepseek-chat",
    "messages": [
        {"role": "system", "content": "You are a helpful assistant"},
        {"role": "user", "content": "你好，请用中文回答"}
    ],
    "temperature": 0.7,
    "stream": False
}

try:
    response = requests.post(API_URL, headers=headers, json=payload)
    response.raise_for_status()  # 自动检测HTTP错误
    result = response.json()
    # 修复了错误的 JSON 解析
    print(result['choices'][0]['message']['content'])
except requests.exceptions.RequestException as e:
    print(f"API请求失败：{str(e)}")
except (KeyError, IndexError) as e:
    print(f"响应解析错误：{str(e)}")