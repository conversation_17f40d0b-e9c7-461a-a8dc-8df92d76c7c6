
'''
这个文件用于连接大模型会话的网站，进行对话，获取对话的内容并返回

会话的网站是：https://yuanbao.tencent.com/

需要先登录，然后进行对话，获取对话的内容，后面一直作为进程挂起等待后续会话

登录触发操作是发起聊天，然后等待登录页面的跳转，然后进行登录，登录后会跳转到聊天页面，然后进行聊天，获取对话的内容
'''
#这里获取对话结果没有区分开推理和答案部分，需要后续在设置的时候进行标记

import os
import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.keys import Keys

# 腾讯元宝网站URL
YUANBAO_URL = "https://yuanbao.tencent.com/"

def init_webdriver(headless=False):
    """初始化Selenium WebDriver
    
    Args:
        headless (bool): 是否使用无头模式
        
    Returns:
        webdriver: 初始化好的WebDriver对象
    """
    chrome_options = Options()
    if headless:
        chrome_options.add_argument("--headless")
    
    # 添加其他必要的选项
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-extensions")
    
    driver = webdriver.Chrome(options=chrome_options)
    return driver

def wait_for_login(driver, timeout=300):
    """等待用户登录
    
    Args:
        driver (webdriver): WebDriver对象
        timeout (int): 超时时间（秒）
        
    Returns:
        bool: 登录是否成功
    """
    print("请在浏览器中完成登录操作...")
    print("登录完成后，页面会自动跳转到聊天界面")
    
    # 等待用户手动登录
    input("登录完成后按Enter键继续...")
    
    # 给页面一些额外的加载时间
    print("正在等待页面加载完成...")
    time.sleep(5)  # 增加等待时间，确保页面有足够时间加载
    
    # 检查是否已经登录成功 - 使用多个选择器和重试机制
    max_retries = 3
    wait_time = 15  # 增加等待时间到15秒
    
    # 扩展选择器列表，增加更多可能的聊天页面特征元素
    selectors = [
        "textarea[placeholder]",  # 通用占位符文本域
        "textarea",  # 任何文本域
        ".block.h-64px",  # 特定类名
        "[data-virtualkeyboard='true']",  # 虚拟键盘属性
        "form textarea",  # 表单中的文本域
        ".chat-input",  # 可能的聊天输入类
        ".message-input",  # 可能的消息输入类
        "div[contenteditable='true']",  # 可编辑div
        ".input-area",  # 输入区域类
        ".chat-container",  # 聊天容器类
        ".conversation",  # 对话类
        ".chat-window"  # 聊天窗口类
    ]
    
    # 尝试检测登录状态
    for attempt in range(max_retries):
        try:
            print(f"检查登录状态 (尝试 {attempt+1}/{max_retries})...")
            
            # 首先检查页面源码中是否包含聊天相关的关键词
            page_source_lower = driver.page_source.lower()
            chat_keywords = ["聊天", "对话", "chat", "conversation", "message", "ai回复", "assistant"]
            
            if any(keyword in page_source_lower for keyword in chat_keywords):
                print("在页面源码中检测到聊天相关关键词，可能已登录成功")
            
            # 尝试使用多个选择器查找聊天页面元素
            for selector in selectors:
                try:
                    element = WebDriverWait(driver, wait_time).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    
                    if element.is_displayed():
                        print(f"找到聊天页面元素，使用选择器: {selector}")
                        # 额外等待一下，确保页面完全加载
                        time.sleep(2)
                        print("登录成功！已进入聊天页面。")
                        return True
                except Exception:
                    continue  # 尝试下一个选择器
            
            # 如果所有选择器都失败，尝试使用XPath
            try:
                # 尝试查找任何文本域或可编辑元素
                xpath_patterns = [
                    "//textarea",
                    "//div[@contenteditable='true']",
                    "//div[contains(@class, 'chat')]",
                    "//div[contains(@class, 'message')]"
                ]
                
                for xpath in xpath_patterns:
                    try:
                        element = WebDriverWait(driver, wait_time).until(
                            EC.presence_of_element_located((By.XPATH, xpath))
                        )
                        
                        if element.is_displayed():
                            print(f"使用XPath找到聊天页面元素: {xpath}")
                            time.sleep(2)
                            print("登录成功！已进入聊天页面。")
                            return True
                    except Exception:
                        continue
            except Exception:
                pass
            
            # 如果还是没找到，检查URL是否包含聊天相关关键词
            current_url = driver.current_url.lower()
            if any(keyword in current_url for keyword in ["chat", "conversation", "message"]):
                print(f"当前URL包含聊天相关关键词: {current_url}")
                print("根据URL判断可能已登录成功")
                return True
            
            # 如果当前尝试失败，但还有重试机会，等待一段时间后重试
            if attempt < max_retries - 1:
                print(f"未检测到聊天页面元素，等待后重试... ({attempt+1}/{max_retries})")
                time.sleep(5)  # 等待5秒后重试
            else:
                print("多次尝试后仍未检测到聊天页面元素")
                
                # 最后尝试：检查页面是否有任何可能的输入元素
                try:
                    inputs = driver.find_elements(By.TAG_NAME, "input")
                    textareas = driver.find_elements(By.TAG_NAME, "textarea")
                    editables = driver.find_elements(By.CSS_SELECTOR, "[contenteditable='true']")
                    
                    if inputs or textareas or editables:
                        print("找到可能的输入元素，尝试继续操作")
                        return True
                except Exception:
                    pass
                
                # 保存页面源码以便调试
                try:
                    with open("login_check_page_source.html", "w", encoding="utf-8") as f:
                        f.write(driver.page_source)
                    print("已保存页面源码到login_check_page_source.html，可用于调试")
                except Exception as e:
                    print(f"保存页面源码失败: {e}")
                
                return False
                
        except Exception as e:
            print(f"检查登录状态时出错: {e}")
            if attempt < max_retries - 1:
                print("等待后重试...")
                time.sleep(5)
            else:
                return False
    
    return False

def trigger_login(driver):
    """触发登录页面
    
    Args:
        driver (webdriver): WebDriver对象
        
    Returns:
        bool: 是否成功触发登录页面
    """
    try:
        # 访问腾讯元宝网站
        driver.get(YUANBAO_URL)
        print("已打开腾讯元宝网站，等待页面加载...")
        time.sleep(5)  # 增加等待时间，确保页面完全加载
        
        # 扩展选择器列表，增加更多可能的入口元素
        selectors = [
            "button.t-button",  # 常见的TDesign按钮
            "button[type='button']",  # 一般按钮
            ".chat-entry",  # 可能的聊天入口类
            ".start-chat",  # 可能的开始聊天类
            "a[href*='chat']",  # 包含chat的链接
            ".whitespace-nowrap:contains('新建对话')",  # 包含"新建对话"文本的元素
            "div[class*='chat']",  # 类名包含chat的div
            "div[class*='new']",  # 类名包含new的div
            "svg + div",  # SVG图标旁边的div
            "[class*='item']:not([class*='selected'])",  # 非选中的item类元素
        ]
        
        # 首先尝试通过文本内容查找元素
        text_patterns = ["新建对话", "开始聊天", "聊天", "对话", "chat"]
        for pattern in text_patterns:
            try:
                # 使用XPath查找包含特定文本的元素
                xpath = f"//div[contains(text(), '{pattern}')] | //button[contains(text(), '{pattern}')] | //a[contains(text(), '{pattern}')]" 
                elements = driver.find_elements(By.XPATH, xpath)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        try:
                            print(f"尝试点击文本元素: {element.text}")
                            # 尝试使用JavaScript点击，更可靠
                            driver.execute_script("arguments[0].click();", element)
                            time.sleep(3)  # 增加等待时间
                            
                            # 检查是否出现了登录相关元素
                            if any(login_text in driver.page_source.lower() for login_text in ["登录", "login", "微信", "qq"]):
                                print("已触发登录页面")
                                return True
                        except Exception as e:
                            print(f"点击文本元素失败: {e}")
                            continue
            except Exception as e:
                print(f"查找文本 {pattern} 失败: {e}")
        
        # 然后尝试CSS选择器
        for selector in selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        try:
                            print(f"尝试点击元素: {element.text if element.text else '无文本元素'}")
                            # 尝试三种点击方式
                            try:
                                # 1. 常规点击
                                element.click()
                            except Exception:
                                try:
                                    # 2. JavaScript点击
                                    driver.execute_script("arguments[0].click();", element)
                                except Exception:
                                    # 3. 使用ActionChains点击
                                    from selenium.webdriver.common.action_chains import ActionChains
                                    ActionChains(driver).move_to_element(element).click().perform()
                            
                            time.sleep(3)  # 增加等待时间
                            
                            # 检查是否出现了登录相关元素
                            if any(login_text in driver.page_source.lower() for login_text in ["登录", "login", "微信", "qq"]):
                                print("已触发登录页面")
                                return True
                        except Exception as e:
                            print(f"点击元素失败: {e}")
                            continue
            except Exception as e:
                print(f"查找元素 {selector} 失败: {e}")
                continue
        
        # 尝试点击页面上所有可见的按钮和链接
        try:
            print("尝试点击所有可见的按钮和链接...")
            buttons = driver.find_elements(By.TAG_NAME, "button")
            links = driver.find_elements(By.TAG_NAME, "a")
            clickable_elements = buttons + links
            
            for element in clickable_elements:
                if element.is_displayed() and element.is_enabled():
                    try:
                        print(f"尝试点击通用元素: {element.text if element.text else '无文本元素'}")
                        driver.execute_script("arguments[0].click();", element)
                        time.sleep(3)
                        
                        # 检查是否出现了登录相关元素
                        if any(login_text in driver.page_source.lower() for login_text in ["登录", "login", "微信", "qq"]):
                            print("已触发登录页面")
                            return True
                    except Exception:
                        continue
        except Exception as e:
            print(f"点击通用元素失败: {e}")
        
        # 尝试直接访问可能的聊天URL
        try:
            print("尝试直接访问聊天URL...")
            chat_urls = [
                YUANBAO_URL + "chat",
                YUANBAO_URL + "#/chat",
                YUANBAO_URL + "#chat"
            ]
            
            for url in chat_urls:
                try:
                    driver.get(url)
                    time.sleep(3)
                    if any(login_text in driver.page_source.lower() for login_text in ["登录", "login", "微信", "qq"]):
                        print(f"通过URL {url} 触发登录页面")
                        return True
                except Exception:
                    continue
        except Exception as e:
            print(f"访问聊天URL失败: {e}")
        
        # 检查是否已经触发了登录页面
        if any(login_text in driver.page_source.lower() for login_text in ["登录", "login", "微信", "qq"]):
            print("已触发登录页面")
            return True
        else:
            print("未能自动触发登录页面，请手动操作")
            print("提示: 请在浏览器中点击任何可能触发登录的按钮")
            # 保存页面源码以便调试
            try:
                with open("login_page_source.html", "w", encoding="utf-8") as f:
                    f.write(driver.page_source)
                print("已保存页面源码到login_page_source.html，可用于调试")
            except Exception as e:
                print(f"保存页面源码失败: {e}")
            return True  # 返回True让用户手动操作
            
    except Exception as e:
        print(f"触发登录页面时出错: {e}")
        return False

def send_message(driver, message):
    """发送消息
    
    Args:
        driver (webdriver): WebDriver对象
        message (str): 要发送的消息
        
    Returns:
        获取的答案
    """
    # 添加限制提示词
    prompt = "在正式回答开始时请以 --[正式回答]-- 标记开始，我的问题是："
    # 确保提示词和用户消息之间有空格，避免文本连接问题
    # 检查message是否为空或None，确保用户输入被正确添加
    if message and message.strip():
        formatted_message = f"{prompt} {message.strip()}"
    else:
        formatted_message = prompt
    print(f"发送消息: {formatted_message}")
    max_retries = 3
    
    for attempt in range(max_retries):
        try:
            print(f"尝试发送消息 (尝试 {attempt+1}/{max_retries})...")
            
            # 保存页面源码以便调试
            try:
                with open("current_page_source.html", "w", encoding="utf-8") as f:
                    f.write(driver.page_source)
                print("已保存当前页面源码到current_page_source.html，用于调试")
            except Exception as e:
                print(f"保存页面源码失败: {e}")
            
            # 扩展的输入框选择器列表
            input_selectors = [
                "textarea[placeholder]",  # 通用占位符文本域
                "textarea[placeholder*='问题']",  # 包含"问题"的占位符
                "textarea[placeholder*='聊天']",  # 包含"聊天"的占位符
                "textarea[placeholder*='发送']",  # 包含"发送"的占位符
                "textarea[placeholder*='输入']",  # 包含"输入"的占位符
                "textarea",  # 任何文本域
                ".block.h-64px",  # 特定类名
                "[data-virtualkeyboard='true']",  # 虚拟键盘属性
                "form textarea",  # 表单中的文本域
                "div[contenteditable='true']",  # 可编辑div
                ".chat-input textarea",  # 聊天输入区域中的文本域
                ".input-area textarea",  # 输入区域中的文本域
                ".message-input textarea",  # 消息输入区域中的文本域
                ".t-textarea__inner",  # TDesign文本域
                ".t-input__inner"  # TDesign输入框
            ]
            
            # 首先尝试CSS选择器
            input_box = None
            for selector in input_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            input_box = element
                            print(f"找到输入框，使用选择器: {selector}")
                            break
                    if input_box:
                        break
                except Exception as e:
                    print(f"使用选择器 {selector} 查找失败: {e}")
                    continue
            
            # 如果CSS选择器失败，尝试XPath
            if not input_box:
                xpath_patterns = [
                    "//textarea",
                    "//div[@contenteditable='true']",
                    "//div[contains(@class, 'input')]/textarea",
                    "//div[contains(@class, 'chat')]/textarea",
                    "//div[contains(@class, 'message')]/textarea",
                    "//div[contains(@class, 't-textarea')]/textarea",
                    "//div[contains(@class, 't-input')]/input"
                ]
                
                for xpath in xpath_patterns:
                    try:
                        elements = driver.find_elements(By.XPATH, xpath)
                        for element in elements:
                            if element.is_displayed() and element.is_enabled():
                                input_box = element
                                print(f"使用XPath找到输入框: {xpath}")
                                break
                        if input_box:
                            break
                    except Exception as e:
                        print(f"使用XPath {xpath} 查找失败: {e}")
                        continue
            
            # 如果仍然找不到输入框，尝试查找所有可能的输入元素
            if not input_box:
                try:
                    # 查找所有可能的输入元素
                    inputs = driver.find_elements(By.TAG_NAME, "input")
                    textareas = driver.find_elements(By.TAG_NAME, "textarea")
                    editables = driver.find_elements(By.CSS_SELECTOR, "[contenteditable='true']")
                    
                    # 合并所有可能的输入元素
                    all_inputs = inputs + textareas + editables
                    
                    # 筛选可见且可用的元素
                    for element in all_inputs:
                        if element.is_displayed() and element.is_enabled():
                            input_box = element
                            print(f"找到可能的输入元素: {element.tag_name}")
                            break
                except Exception as e:
                    print(f"查找所有可能的输入元素失败: {e}")
            
            # 如果仍然找不到输入框
            if not input_box:
                if attempt == max_retries - 1:
                    print("无法找到输入框")
                    return False
                print("未找到输入框，等待后重试...")
                time.sleep(2)
                continue
            
            # 确保输入框可交互
            if not (input_box.is_displayed() and input_box.is_enabled()):
                print("输入框不可交互")
                if attempt == max_retries - 1:
                    return False
                time.sleep(2)
                continue
            
            # 清空输入框并输入消息 - 使用多种方法
            try:
                # 检查元素类型，对contenteditable元素使用特殊处理
                if input_box.get_attribute("contenteditable") == "true":
                    print("检测到contenteditable元素，使用特殊处理")
                    # 方法1: 使用JavaScript清空contenteditable元素
                    driver.execute_script("arguments[0].innerHTML = '';", input_box)
                    # 方法2: 使用JavaScript设置contenteditable元素的内容
                    # 确保formatted_message不为空且正确包含用户输入的内容
                    if message and message.strip():
                        driver.execute_script("arguments[0].innerHTML = arguments[1];", input_box, formatted_message)
                    else:
                        driver.execute_script("arguments[0].innerHTML = arguments[1];", input_box, prompt)
                    # 额外触发输入事件，确保网站检测到内容变化
                    driver.execute_script("""
                        var event = new Event('input', {
                            bubbles: true,
                            cancelable: true,
                        });
                        arguments[0].dispatchEvent(event);
                        
                        // 触发更多事件以确保内容变化被检测到
                        var changeEvent = new Event('change', {
                            bubbles: true,
                            cancelable: true,
                        });
                        arguments[0].dispatchEvent(changeEvent);
                        
                        var keydownEvent = new KeyboardEvent('keydown', {
                            key: 'a',
                            code: 'KeyA',
                            keyCode: 65,
                            which: 65,
                            bubbles: true
                        });
                        arguments[0].dispatchEvent(keydownEvent);
                    """, input_box)
                    
                    # 使用ActionChains模拟真实的用户输入
                    try:
                        from selenium.webdriver.common.action_chains import ActionChains
                        actions = ActionChains(driver)
                        actions.move_to_element(input_box).click().perform()
                        time.sleep(0.5)
                        # 先清空，然后输入内容
                        actions.key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).perform()
                        time.sleep(0.5)
                        actions.send_keys(Keys.DELETE).perform()
                        time.sleep(0.5)
                        # 确保formatted_message包含用户输入的内容
                        if message and message.strip():
                            actions.send_keys(formatted_message).perform()
                        else:
                            actions.send_keys(prompt).perform()
                        time.sleep(0.5)
                    except Exception as e:
                        print(f"ActionChains模拟输入失败: {e}")
                        # 继续使用JavaScript方法
                else:
                    # 方法1: 常规清空和输入（适用于textarea等标准输入元素）
                    input_box.clear()
                    input_box.send_keys(formatted_message)
            except Exception as e:
                print(f"常规输入失败: {e}，尝试JavaScript输入")
                try:
                    # 方法2: 使用JavaScript设置值（通用方法）
                    if input_box.tag_name.lower() == "div":
                        driver.execute_script("arguments[0].innerHTML = arguments[1];", input_box, formatted_message)
                    else:
                        driver.execute_script("arguments[0].value = arguments[1];", input_box, formatted_message)
                    
                    # 触发输入事件
                    driver.execute_script("""
                        var event = new Event('input', {
                            bubbles: true,
                            cancelable: true,
                        });
                        arguments[0].dispatchEvent(event);
                    """, input_box)
                except Exception as e:
                    print(f"JavaScript输入也失败: {e}")
                    if attempt == max_retries - 1:
                        return False
                    time.sleep(2)
                    continue
            
            # 等待确保消息已输入
            time.sleep(1)
            
            # 查找并点击发送按钮 - 使用更精确的选择器
            # 参考cve_poc_extractor.py中的实现，使用更精确的选择器
            send_button_selectors = [
                # 腾讯元宝网站特定选择器
                "div.sendBtn",  # 腾讯元宝网站中常见的发送按钮类
                "button.sendBtn",  # 按钮形式的发送按钮
                "div[class*='sendBtn']",  # 类名包含sendBtn的div
                "button[class*='sendBtn']",  # 类名包含sendBtn的button
                ".t-button[type='submit']",  # TDesign提交按钮
                ".t-button.t-button--primary",  # TDesign主要按钮
                ".t-button.t-button--theme-primary",  # TDesign主题按钮
                "button.t-button--primary",  # TDesign主要按钮
                "button[aria-label='发送']",  # 带有发送标签的按钮
                "button[title='发送']",  # 带有发送标题的按钮
                "div.chat-footer button",  # 聊天页脚中的按钮
                "div.chat-input-actions button",  # 聊天输入操作区域中的按钮
                "div.chat-input-container button",  # 聊天输入容器中的按钮
                "div.chat-input-wrapper button",  # 聊天输入包装器中的按钮
                "div.chat-input-area button",  # 聊天输入区域中的按钮
                "div.chat-input-box button",  # 聊天输入框中的按钮
                "div.chat-input-footer button",  # 聊天输入页脚中的按钮
                "div.chat-input-actions button:last-child",  # 聊天输入操作区域中的最后一个按钮
                "div.chat-input-container button:last-child",  # 聊天输入容器中的最后一个按钮
                "div.chat-input-wrapper button:last-child",  # 聊天输入包装器中的最后一个按钮
                "div.chat-input-area button:last-child",  # 聊天输入区域中的最后一个按钮
                "div.chat-input-box button:last-child",  # 聊天输入框中的最后一个按钮
                "div.chat-input-footer button:last-child",  # 聊天输入页脚中的最后一个按钮
                # 通用选择器
                "button[type='submit']",  # 提交类型按钮
                "form button",  # 表单中的按钮
                "button:last-child",  # 最后一个按钮
                ".t-button",  # TDesign按钮
                "button.t-button",  # TDesign按钮
                "svg + button",  # SVG图标旁边的按钮
                "div[class*='send']",  # 类名包含send的div
                "div[class*='submit']",  # 类名包含submit的div
                "span[class*='send']",  # 类名包含send的span
                "i[class*='send']"  # 类名包含send的i元素
            ]
            
            # 首先尝试查找页面上所有可能的按钮，并检查其文本内容
            buttons = driver.find_elements(By.TAG_NAME, "button")
            send_button = None
            for button in buttons:
                try:
                    if button.is_displayed() and button.is_enabled():
                        button_text = button.text.lower()
                        # 检查按钮文本是否包含发送相关的关键词
                        if any(keyword in button_text for keyword in ["发送", "send", "提交", "submit"]):
                            send_button = button
                            print(f"通过文本内容找到发送按钮: {button_text}")
                            break
                except Exception as e:
                    print(f"检查按钮文本时出错: {e}")
                    continue
            
            # 如果通过文本内容未找到发送按钮，则尝试使用选择器
            if not send_button:
                for selector in send_button_selectors:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in elements:
                            if element.is_displayed() and element.is_enabled():
                                send_button = element
                                print(f"找到发送按钮，使用选择器: {selector}")
                                break
                        if send_button:
                            break
                    except Exception:
                        continue
            
            if not send_button:
                # 尝试使用Enter键发送
                try:
                    print("未找到发送按钮，尝试使用Enter键发送")
                    # 对于contenteditable元素，需要特殊处理
                    if input_box.get_attribute("contenteditable") == "true":
                        # 使用JavaScript模拟Enter键
                        driver.execute_script("""
                            var event = new KeyboardEvent('keydown', {
                                key: 'Enter',
                                code: 'Enter',
                                keyCode: 13,
                                which: 13,
                                bubbles: true
                            });
                            arguments[0].dispatchEvent(event);
                        """, input_box)
                    else:
                        input_box.send_keys(Keys.RETURN)
                    
                    time.sleep(2)  # 等待可能的发送操作
                    
                    # 检查消息是否已发送
                    is_sent = False
                    if input_box.get_attribute("contenteditable") == "true":
                        # 对于contenteditable元素，检查innerHTML是否为空
                        content = driver.execute_script("return arguments[0].innerHTML;", input_box)
                        is_sent = content == "" or content == "<br>" or content == "<p></p>"
                    else:
                        # 对于普通输入元素，检查value是否为空
                        is_sent = input_box.get_attribute("value") == ""
                    
                    if is_sent:
                        print("消息似乎已通过Enter键发送")
                        return True
                except Exception as e:
                    print(f"使用Enter键发送失败: {e}")
                    if attempt == max_retries - 1:
                        return False
                    time.sleep(2)
                    continue
            else:
                # 尝试点击发送按钮 - 使用更可靠的方法
                print(f"尝试点击发送按钮: {send_button.tag_name}")
                
                # 首先尝试使用更可靠的方法 - 参考cve_poc_extractor.py中的实现
                try:
                    # 保存按钮的位置和大小，用于调试
                    button_location = send_button.location
                    button_size = send_button.size
                    print(f"发送按钮位置: {button_location}, 大小: {button_size}")
                    
                    # 确保按钮在视图中
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", send_button)
                    time.sleep(0.5)
                    
                    # 方法1: 使用JavaScript直接触发点击事件 - 最可靠的方法
                    print("使用JavaScript直接触发点击事件")
                    driver.execute_script("""
                        // 先触发鼠标事件，然后触发点击事件
                        var mouseoverEvent = new MouseEvent('mouseover', {
                            bubbles: true,
                            cancelable: true,
                            view: window
                        });
                        arguments[0].dispatchEvent(mouseoverEvent);
                        
                        setTimeout(function() {
                            var mousedownEvent = new MouseEvent('mousedown', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            });
                            arguments[0].dispatchEvent(mousedownEvent);
                            
                            setTimeout(function() {
                                var clickEvent = new MouseEvent('click', {
                                    bubbles: true,
                                    cancelable: true,
                                    view: window
                                });
                                arguments[0].dispatchEvent(clickEvent);
                                
                                setTimeout(function() {
                                    var mouseupEvent = new MouseEvent('mouseup', {
                                        bubbles: true,
                                        cancelable: true,
                                        view: window
                                    });
                                    arguments[0].dispatchEvent(mouseupEvent);
                                }, 50);
                            }, 50);
                        }, 50);
                    """, send_button)
                    time.sleep(1.5)  # 给予足够的时间处理事件
                    
                    # 检查消息是否已发送
                    if input_box.get_attribute("value") == "" or (input_box.get_attribute("contenteditable") == "true" and 
                        (driver.execute_script("return arguments[0].innerHTML;", input_box) == "" or 
                         driver.execute_script("return arguments[0].innerHTML;", input_box) == "<br>" or 
                         driver.execute_script("return arguments[0].innerHTML;", input_box) == "<p></p>")):
                        print("JavaScript事件触发成功，消息已发送")
                        return True
                except Exception as e:
                    print(f"JavaScript事件触发失败: {e}")
                
                # 方法2: 使用ActionChains模拟真实的用户点击
                try:
                    print("使用ActionChains模拟真实的用户点击")
                    from selenium.webdriver.common.action_chains import ActionChains
                    actions = ActionChains(driver)
                    # 移动到元素中心并点击
                    actions.move_to_element(send_button).pause(0.5).click().pause(0.5).perform()
                    time.sleep(1)  # 等待点击处理
                    
                    # 检查消息是否已发送
                    if input_box.get_attribute("value") == "" or (input_box.get_attribute("contenteditable") == "true" and 
                        (driver.execute_script("return arguments[0].innerHTML;", input_box) == "" or 
                         driver.execute_script("return arguments[0].innerHTML;", input_box) == "<br>" or 
                         driver.execute_script("return arguments[0].innerHTML;", input_box) == "<p></p>")):
                        print("ActionChains点击成功，消息已发送")
                        return True
                except Exception as e:
                    print(f"ActionChains点击失败: {e}")
                
                # 方法3: 常规点击
                try:
                    print("使用常规点击方法")
                    send_button.click()
                    time.sleep(1)  # 等待点击处理
                    
                    # 检查消息是否已发送
                    if input_box.get_attribute("value") == "" or (input_box.get_attribute("contenteditable") == "true" and 
                        (driver.execute_script("return arguments[0].innerHTML;", input_box) == "" or 
                         driver.execute_script("return arguments[0].innerHTML;", input_box) == "<br>" or 
                         driver.execute_script("return arguments[0].innerHTML;", input_box) == "<p></p>")):
                        print("常规点击成功，消息已发送")
                        return True
                except Exception as e:
                    print(f"常规点击失败: {e}")
                
                # 方法4: 尝试使用Enter键发送
                try:
                    print("尝试使用Enter键发送")
                    # 确保输入框处于焦点状态
                    driver.execute_script("arguments[0].focus();", input_box)
                    time.sleep(0.5)
                    
                    if input_box.get_attribute("contenteditable") == "true":
                        # 使用JavaScript模拟Enter键按下和释放
                        driver.execute_script("""
                            var keydownEvent = new KeyboardEvent('keydown', {
                                key: 'Enter',
                                code: 'Enter',
                                keyCode: 13,
                                which: 13,
                                bubbles: true
                            });
                            arguments[0].dispatchEvent(keydownEvent);
                            
                            setTimeout(function() {
                                var keyupEvent = new KeyboardEvent('keyup', {
                                    key: 'Enter',
                                    code: 'Enter',
                                    keyCode: 13,
                                    which: 13,
                                    bubbles: true
                                });
                                arguments[0].dispatchEvent(keyupEvent);
                            }, 50);
                        """, input_box)
                    else:
                        # 对于普通输入框，使用send_keys
                        input_box.send_keys(Keys.RETURN)
                    
                    time.sleep(1.5)  # 给予足够的时间处理事件
                    
                    # 检查消息是否已发送
                    if input_box.get_attribute("value") == "" or (input_box.get_attribute("contenteditable") == "true" and 
                        (driver.execute_script("return arguments[0].innerHTML;", input_box) == "" or 
                         driver.execute_script("return arguments[0].innerHTML;", input_box) == "<br>" or 
                         driver.execute_script("return arguments[0].innerHTML;", input_box) == "<p></p>")):
                        print("Enter键发送成功，消息已发送")
                        return True
                except Exception as e:
                    print(f"Enter键发送失败: {e}")
                    if attempt == max_retries - 1:
                        return False
                    time.sleep(2)
                    continue
            
            # 等待消息发送 - 增加等待时间
            time.sleep(3)
            
            # 检查消息是否已发送（根据元素类型使用不同的检查方法）
            try:
                is_sent = False
                if input_box.get_attribute("contenteditable") == "true":
                    # 对于contenteditable元素，检查innerHTML是否为空或只包含空白标签
                    content = driver.execute_script("return arguments[0].innerHTML;", input_box)
                    is_sent = content == "" or content == "<br>" or content == "<p></p>" or content == "<p><br></p>"
                    print(f"Contenteditable内容: '{content}'")
                    
                    # 如果内容未清空，但我们已经尝试了所有方法，假设消息已发送
                    if not is_sent and attempt == max_retries - 1:
                        print("尽管输入框未清空，但已尝试所有方法，假设消息已发送")
                        return True
                else:
                    # 对于普通输入元素，检查value是否为空
                    value = input_box.get_attribute("value")
                    is_sent = value == ""
                    print(f"输入框值: '{value}'")
                    
                    # 如果内容未清空，但我们已经尝试了所有方法，假设消息已发送
                    if not is_sent and attempt == max_retries - 1:
                        print("尽管输入框未清空，但已尝试所有方法，假设消息已发送")
                        return True
                
                if is_sent:
                    print("消息已成功发送")
                    return True
                else:
                    print("消息可能未发送，输入框未清空")
                    if attempt == max_retries - 1:
                        # 最后一次尝试，检查页面上是否有新消息出现
                        try:
                            # 等待一段时间，看是否有新消息出现
                            time.sleep(2)
                            # 假设消息已发送，因为我们已经尝试了所有方法
                            print("已尝试所有方法，假设消息已发送")
                            return True
                        except Exception:
                            return False
            except Exception as e:
                # 如果无法检查输入框值，记录错误并假设消息已发送
                print(f"无法检查输入框状态: {e}，假设消息已发送")
                return True
                
        except Exception as e:
            print(f"发送消息时出错: {e}")
            if attempt == max_retries - 1:
                return False
            time.sleep(2)
    
    return False

def get_response(driver, timeout=120):
    """获取AI回复
    
    Args:
        driver (webdriver): WebDriver对象
        timeout (int): 等待回复的超时时间（秒），默认增加到120秒以处理较长的生成时间
        
    Returns:
        dict: 包含AI回复的字典，格式为{"analysis": "分析过程", "answer": "最终回答"}
    """
    
    def split_response(content):
        """根据格式特征拆分回答"""
        reasoning = ""
        answer = ""
        
        # 尝试按格式拆分
        reasoning_start = content.find("[推理过程]")
        answer_start = content.find("[正式回答]")
        
        if reasoning_start != -1 and answer_start != -1:
            reasoning = content[reasoning_start+6:answer_start].strip()
            answer = content[answer_start+6:].strip()
        else:
            # 如果没有找到格式标记，将整个内容作为回答
            answer = content
            
        return reasoning, answer
    start_time = time.time()
    max_retries = 3
    wait_interval = 5  # 每次重试间隔秒数
    
    # 尝试从网络请求中获取回答内容
    try:
        logs = driver.get_log('performance')
        for entry in logs:
            message = json.loads(entry['message'])
            if 'message' in message and 'params' in message['message']:
                request = message['message']['params'].get('request', {})
                if 'hunyuan.tencent.com' in request.get('url', '') and request.get('method') == 'POST':
                    response = message['message']['params'].get('response', {})
                    if 'body' in response:
                        try:
                            body = json.loads(response['body'])
                            if 'answer' in body:
                                return {"analysis": "", "answer": body['answer']}
                            elif 'content' in body:
                                return {"analysis": "", "answer": body['content']}
                            elif 'text' in body:
                                return {"analysis": "", "answer": body['text']}
                        except json.JSONDecodeError:
                            # 尝试解析非标准JSON格式的响应体
                            body_text = response['body']
                            if 'answer' in body_text.lower():
                                return {"analysis": "", "answer": body_text}
    except Exception as e:
        print(f"从网络请求获取回答失败: {e}")
        
    # 检查页面中可能包含回答的隐藏元素
    try:
        hidden_elements = driver.find_elements(By.CSS_SELECTOR, '[style*="display: none"], [hidden]')
        for element in hidden_elements:
            if 'answer' in element.text.lower() or '回复' in element.text:
                driver.execute_script("arguments[0].style.display = 'block';", element)
                return {"analysis": "", "answer": element.text}
    except Exception as e:
        print(f"检查隐藏元素失败: {e}")
    
    # 扩展选择器列表，增加更多可能的回复元素选择器
    selectors = [
        ".markdown-body",  # 常见的Markdown内容容器
        ".chat-message-content",  # 可能的聊天消息内容类
        ".ai-response",  # 可能的AI回复类
        ".message-content",  # 通用消息内容类
        "[role='assistant']",  # 基于角色的选择器
        ".chat-bubble",  # 聊天气泡类
        ".message-reply",  # 从HTML中发现的回复类
        ".leading-relaxed",  # 从HTML中发现的内容类
        ".text-black",  # 从HTML中发现的文本类
        ".whitespace-pre-wrap",  # 预格式化文本包装器
        "div[type='assistant']",  # 基于类型的选择器
        "div[asrawtext='false'][type='assistant']",  # 特定属性组合
        ".overflow-hidden div.text-wrap",  # 嵌套结构
        ".flex-row .text-black",  # 特定布局中的文本
        ".t-dialog__body",  # TDesign对话框内容
        ".t-dialog__content",  # TDesign对话框内容
        ".t-dialog__body p",  # TDesign对话框段落
        ".t-dialog__body div",  # TDesign对话框div
        ".t-dialog__content p",  # TDesign对话框段落
        ".t-dialog__content div",  # TDesign对话框div
        ".t-popup__content",  # TDesign弹出内容
        ".t-popup__content p",  # TDesign弹出段落
        ".t-popup__content div",  # TDesign弹出div
        "p.text-wrap",  # 文本包装段落
        "div.text-wrap",  # 文本包装div
        "div[class*='message']",  # 类名包含message的div
        "div[class*='chat']",  # 类名包含chat的div
        "div[class*='reply']",  # 类名包含reply的div
        "div[class*='response']",  # 类名包含response的div
        "div[class*='answer']",  # 类名包含answer的div
        "div[class*='ai']",  # 类名包含ai的div
        "div[class*='assistant']",  # 类名包含assistant的div
        "div[class*='content']",  # 类名包含content的div
        "div[class*='text']",  # 类名包含text的div
        "p[class*='text']",  # 类名包含text的p
        "p[class*='content']",  # 类名包含content的p
        "p[class*='message']",  # 类名包含message的p
        "p[class*='reply']",  # 类名包含reply的p
        "p[class*='response']",  # 类名包含response的p
        "p[class*='answer']",  # 类名包含answer的p
        "p[class*='ai']",  # 类名包含ai的p
        "p[class*='assistant']"  # 类名包含assistant的p
    ]
    
    # XPath选择器，用于更复杂的选择
    xpath_selectors = [
        "//div[@type='assistant']//div[contains(@class, 'text-wrap')]",  # 助手类型下的文本包装
        "//div[contains(@class, 'message-reply')]//div[contains(@class, 'leading-relaxed')]",  # 消息回复中的文本
        "//div[contains(@class, 'overflow-hidden')]//div[contains(@class, 'text-black')]",  # 隐藏溢出容器中的文本
        "//div[contains(@class, 'flex-row')]//div[contains(@class, 'text-black')]",  # 行布局中的文本
        "//div[contains(@class, 'markdown-body')]",  # Markdown内容
        "//div[contains(text(), 'AI') and contains(@class, 'reply')]/following-sibling::div",  # AI回复后的内容
        "//div[contains(@class, 't-dialog__body')]",  # TDesign对话框内容
        "//div[contains(@class, 't-dialog__content')]",  # TDesign对话框内容
        "//div[contains(@class, 't-popup__content')]",  # TDesign弹出内容
        "//div[contains(@class, 'message')]",  # 类名包含message的div
        "//div[contains(@class, 'chat')]",  # 类名包含chat的div
        "//div[contains(@class, 'reply')]",  # 类名包含reply的div
        "//div[contains(@class, 'response')]",  # 类名包含response的div
        "//div[contains(@class, 'answer')]",  # 类名包含answer的div
        "//div[contains(@class, 'ai')]",  # 类名包含ai的div
        "//div[contains(@class, 'assistant')]",  # 类名包含assistant的div
        "//div[contains(@class, 'content')]",  # 类名包含content的div
        "//div[contains(@class, 'text')]",  # 类名包含text的div
        "//p[contains(@class, 'text')]",  # 类名包含text的p
        "//p[contains(@class, 'content')]",  # 类名包含content的p
        "//p[contains(@class, 'message')]",  # 类名包含message的p
        "//p[contains(@class, 'reply')]",  # 类名包含reply的p
        "//p[contains(@class, 'response')]",  # 类名包含response的p
        "//p[contains(@class, 'answer')]",  # 类名包含answer的p
        "//p[contains(@class, 'ai')]",  # 类名包含ai的p
        "//p[contains(@class, 'assistant')]"  # 类名包含assistant的p
    ]
    
    for attempt in range(max_retries):
        try:
            print(f"尝试获取AI回复 (尝试 {attempt+1}/{max_retries})...")
            
            # 首先检查是否有加载指示器或正在生成的标志
            try:
                # 等待可能的加载指示器消失
                loading_selectors = [
                    ".loading", ".generating", ".thinking", ".typing", ".waiting",
                    "[class*='loading']", "[class*='generating']", "[class*='typing']"
                ]
                for loading_selector in loading_selectors:
                    try:
                        loading_elements = driver.find_elements(By.CSS_SELECTOR, loading_selector)
                        if loading_elements and any(elem.is_displayed() for elem in loading_elements):
                            print(f"检测到加载指示器: {loading_selector}，等待其消失...")
                            WebDriverWait(driver, min(timeout/2, 30)).until_not(
                                EC.visibility_of_any_elements_located((By.CSS_SELECTOR, loading_selector))
                            )
                    except Exception:
                        continue
            except Exception as e:
                print(f"检查加载状态时出错: {e}")
            
            # 等待一小段时间，确保回复已加载
            time.sleep(3)
            
            # 尝试CSS选择器
            response_elements = None
            for selector in selectors:
                try:
                    # 使用较短的超时时间尝试每个选择器
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    visible_elements = [e for e in elements if e.is_displayed() and e.text.strip()]
                    if visible_elements:
                        response_elements = visible_elements
                        print(f"找到回复元素，使用选择器: {selector}")
                        break
                except Exception as e:
                    print(f"使用选择器 {selector} 查找失败: {e}")
                    continue
            
            # 如果CSS选择器失败，尝试XPath
            if not response_elements:
                for xpath in xpath_selectors:
                    try:
                        elements = driver.find_elements(By.XPATH, xpath)
                        visible_elements = [e for e in elements if e.is_displayed() and e.text.strip()]
                        if visible_elements:
                            response_elements = visible_elements
                            print(f"找到回复元素，使用XPath: {xpath}")
                            break
                    except Exception as e:
                        print(f"使用XPath {xpath} 查找失败: {e}")
                        continue
            
            # 如果找到了回复元素
            if response_elements and len(response_elements) > 0:
                # 获取最新的回复，但需要过滤掉"新建对话"等非回复内容
                for element in reversed(response_elements):
                    text = element.text.strip()
                    # 过滤掉导航元素、按钮文本等非回复内容
                    if text and text not in ["新建对话", "聊天", "对话", "chat"]:
                        # 检查文本长度和内容特征，判断是否为AI回复
                        if len(text) > 10 and not text.startswith("#") and not text.startswith(">"):
                            # 进一步验证文本内容是否符合AI回复特征
                            if any(keyword in text.lower() for keyword in ["你好", "您好", "回复", "帮助", "问题", "测试", "聊天", "可以", "想"]):
                                print("成功获取AI回复")
                                # 尝试分离分析过程和最终回答
                                if "分析：" in text and "回答：" in text:
                                    parts = text.split("分析：")
                                    analysis = parts[1].split("回答：")[0].strip()
                                    answer = parts[1].split("回答：")[1].strip()
                                    return {"analysis": analysis, "answer": answer}
                                else:
                                    # 提取纯文本回复内容，去除多余的JSON格式信息
                                    clean_text = text.split("AI回复: ")[-1].replace("{", "").replace("}", "").replace("'", "").strip()
                                    return {"analysis": "", "answer": clean_text}
                
                # 如果上面的过滤条件太严格，尝试宽松一点的条件
                for element in reversed(response_elements):
                    text = element.text.strip()
                    if text and text not in ["新建对话", "聊天", "对话", "chat"] and len(text) > 15:
                        print("成功获取AI回复（宽松条件）")
                        # 提取纯文本回复内容，去除多余的JSON格式信息
                        clean_text = text.split("AI回复: ")[-1].replace("{", "").replace("}", "").replace("'", "").strip()
                        return {"analysis": "", "answer": clean_text}
                
                print("找到回复元素，但内容可能不是有效的AI回复")
            else:
                print("找到回复元素，但内容为空")
            
            # 如果没有找到回复元素或内容为空
            print(f"尝试 {attempt+1}: 未找到有效的回复元素")
            
            # 检查是否超时
            elapsed_time = time.time() - start_time
            if elapsed_time >= timeout:
                print(f"等待回复超时 ({timeout}秒)")
                return {"analysis": "", "answer": "等待回复超时，请检查网络连接或重试"}
            
            # 如果还有重试次数，等待后继续
            if attempt < max_retries - 1:
                print(f"等待 {wait_interval} 秒后重试...")
                time.sleep(wait_interval)
            else:
                # 最后一次尝试，直接检查页面上所有可能的文本元素
                print("尝试直接检查页面上所有可能的文本元素...")
                try:
                    # 保存页面源码以便调试
                    with open("response_page_source.html", "w", encoding="utf-8") as f:
                        f.write(driver.page_source)
                    print("已保存页面源码到response_page_source.html，可用于调试")
                    
                    # 查找所有可能包含文本的元素
                    text_elements = []
                    for tag in ['p', 'div', 'span', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                        elements = driver.find_elements(By.TAG_NAME, tag)
                        for element in elements:
                            if element.is_displayed() and element.text.strip():
                                text_elements.append(element)
                    
                    # 按照元素在页面上的位置排序（从上到下）
                    text_elements.sort(key=lambda e: e.location['y'])
                    
                    # 查找最近添加的文本元素（假设AI回复是最近添加的）
                    recent_texts = []
                    for element in text_elements:
                        text = element.text.strip()
                        # 过滤掉导航元素、按钮文本等非回复内容
                        if text and text not in ["新建对话", "聊天", "对话", "chat"]:
                            # 检查文本长度和内容特征
                            if len(text) > 15:  # AI回复通常较长
                                recent_texts.append(text)
                    
                    # 如果找到了可能的回复文本
                    if recent_texts:
                        print(f"找到 {len(recent_texts)} 个可能的回复文本")
                        
                        # 首先尝试找到包含关键词的文本
                        for text in reversed(recent_texts):
                            if any(keyword in text.lower() for keyword in ["你好", "您好", "回复", "帮助", "问题", "测试", "聊天", "可以", "想"]):
                                print(f"找到包含关键词的回复: {text[:50]}...")
                                return text
                        
                        # 如果没有找到包含关键词的文本，取最后一个（假设是最新的回复）
                        latest_text = recent_texts[-1]
                        print(f"使用最后一个文本作为回复: {latest_text[:50]}...")
                        return latest_text
                except Exception as e:
                    print(f"直接检查文本元素失败: {e}")
                
                print("所有尝试均失败，无法获取AI回复")
                return "无法获取AI回复，可能是页面结构变化或网络问题"
                
        except TimeoutException:
            print(f"尝试 {attempt+1}: 等待回复超时")
            if attempt == max_retries - 1:
                return {"analysis": "", "answer": "等待回复超时，请检查网络连接或重试"}
        except Exception as e:
            print(f"尝试 {attempt+1}: 获取回复时出错: {e}")
            if attempt == max_retries - 1:
                return {"analysis": "", "answer": f"获取回复时出错: {e}，请重试"}
    
    # 如果所有尝试都失败
    return {"analysis": "", "answer": "无法获取AI回复，请检查网络连接或重试"}

def chat_session(message=None):
    """启动一个聊天会话
    
    Args:
        message (str, optional): 初始消息
        
    Returns:
        tuple: (driver, success) WebDriver对象和是否成功的标志
    """
    driver = init_webdriver(headless=False)
    
    try:
        # 触发登录
        if not trigger_login(driver):
            print("无法触发登录页面")
            return driver, False
        
        # 等待用户登录
        if not wait_for_login(driver):
            print("登录失败")
            return driver, False
            
        # 确认聊天界面已准备就绪
        print("登录成功，正在确认聊天界面状态...")
        time.sleep(2)  # 给页面一些加载时间
        
        # 尝试发送一个测试消息来验证聊天功能是否正常
        if not message:
            # 如果用户没有提供初始消息，发送一个简单的测试消息
            test_message = "你好"
            print(f"发送测试消息: {test_message}")
            if send_message(driver, test_message):
                response = get_response(driver)
                print(f"AI回复: {response}")
                # 如果成功获取回复，说明聊天功能正常
                print("聊天功能正常，会话已准备就绪")
            else:
                print("发送测试消息失败，但将继续尝试")
        else:
            # 如果提供了初始消息，则发送
            if send_message(driver, message):
                response = get_response(driver)
                print(f"AI回复: {response}")
            else:
                print("发送初始消息失败")
        
        return driver, True
    except Exception as e:
        print(f"聊天会话初始化出错: {e}")
        return driver, False

def main():
    """主函数"""
    print("启动腾讯元宝聊天会话...")
    
    # 初始化会话
    driver, success = chat_session()
    
    if not success:
        print("会话初始化失败，程序退出")
        driver.quit()
        return
    
    print("\n会话已准备就绪，可以开始聊天")
    print("输入 'exit' 或 'quit' 结束会话")
    
    # 交互式聊天循环
    while True:
        user_input = input("\n请输入消息: ")
        
        if user_input.lower() in ['exit', 'quit']:
            break
        
        # 确保用户输入不为空且被正确传递
        if user_input and user_input.strip():
            if send_message(driver, user_input.strip()):
                print("等待AI回复...")
                response = get_response(driver)
                print(f"\nAI回复: {response}")
        else:
            print("消息发送失败")
    
    print("会话结束")
    driver.quit()

if __name__ == "__main__":
    main()