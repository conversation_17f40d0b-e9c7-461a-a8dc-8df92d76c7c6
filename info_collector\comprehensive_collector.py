"""
全面信息收集器 - 全自动全智能全网漏洞分析器核心模块

功能：
1. 网络层信息收集：主机发现、端口扫描、服务识别、OS识别
2. 应用层信息收集：Web应用、数据库、中间件、API接口
3. 系统层信息收集：文件系统、用户账户、网络配置、安全设置
4. 威胁情报收集：外部威胁情报、内部安全状态、历史攻击记录
5. 智能分析：AI驱动的漏洞识别、风险评估、攻击路径生成

作者：AI渗透测试系统
版本：1.0.0
"""

import os
import sys
import json
import time
import asyncio
import ipaddress
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 导入现有模块
from info_collector.nmap.nmapscan import NmapScanner
from info_collector.web.api_scan import scan_api
from info_collector.web.web_content_spider import crawl_website
from info_collector.web.awvs_scan import perform_awvs_scan
from info_concentrator.effective_info_extraction import get_db_connection, analyze_with_llm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ComprehensiveCollector:
    """
    全面信息收集器
    
    集成多种扫描技术，提供全方位的信息收集能力
    """
    
    def __init__(self, max_workers: int = 10):
        """
        初始化收集器
        
        Args:
            max_workers: 最大并发工作线程数
        """
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 初始化各种收集器
        self.network_collector = NetworkCollector()
        self.web_collector = WebCollector()
        self.system_collector = SystemCollector()
        self.threat_intel_collector = ThreatIntelCollector()
        self.ai_analyzer = AISecurityAnalyzer()
        
        logger.info(f"全面信息收集器初始化完成，最大并发数：{max_workers}")
    
    def collect_target_info(self, target: str) -> Dict[str, Any]:
        """
        收集目标的全面信息
        
        Args:
            target: 目标IP、域名或IP段
            
        Returns:
            Dict: 包含所有收集信息的字典
        """
        logger.info(f"开始收集目标信息：{target}")
        start_time = time.time()
        
        # 确定目标类型
        target_type = self._determine_target_type(target)
        logger.info(f"目标类型：{target_type}")
        
        # 并发收集各类信息
        collection_tasks = []
        
        # 网络层信息收集
        collection_tasks.append(
            self.executor.submit(self.network_collector.collect, target)
        )
        
        # 如果是单个IP或域名，进行深度收集
        if target_type in ['ip', 'domain']:
            collection_tasks.extend([
                self.executor.submit(self.web_collector.collect, target),
                self.executor.submit(self.system_collector.collect, target),
                self.executor.submit(self.threat_intel_collector.collect, target)
            ])
        
        # 等待所有任务完成
        results = {
            'target': target,
            'target_type': target_type,
            'collection_time': datetime.now().isoformat(),
            'network_info': {},
            'web_info': {},
            'system_info': {},
            'threat_intel': {},
            'ai_analysis': {}
        }
        
        for i, future in enumerate(as_completed(collection_tasks)):
            try:
                result = future.result(timeout=300)  # 5分钟超时
                
                if i == 0:  # 网络信息
                    results['network_info'] = result
                elif i == 1:  # Web信息
                    results['web_info'] = result
                elif i == 2:  # 系统信息
                    results['system_info'] = result
                elif i == 3:  # 威胁情报
                    results['threat_intel'] = result
                    
            except Exception as e:
                logger.error(f"收集任务失败：{e}")
        
        # AI智能分析
        try:
            results['ai_analysis'] = self.ai_analyzer.analyze_comprehensive(results)
        except Exception as e:
            logger.error(f"AI分析失败：{e}")
            results['ai_analysis'] = {'error': str(e)}
        
        collection_time = time.time() - start_time
        results['collection_duration'] = collection_time
        
        logger.info(f"目标信息收集完成，耗时：{collection_time:.2f}秒")
        return results
    
    def _determine_target_type(self, target: str) -> str:
        """
        确定目标类型
        
        Args:
            target: 目标字符串
            
        Returns:
            str: 目标类型 ('ip', 'domain', 'ip_range', 'url')
        """
        try:
            # 尝试解析为IP地址
            ipaddress.ip_address(target)
            return 'ip'
        except ValueError:
            pass
        
        try:
            # 尝试解析为IP网段
            ipaddress.ip_network(target, strict=False)
            return 'ip_range'
        except ValueError:
            pass
        
        # 检查是否为URL
        if target.startswith(('http://', 'https://')):
            return 'url'
        
        # 默认为域名
        return 'domain'


class NetworkCollector:
    """网络层信息收集器"""
    
    def __init__(self):
        self.nmap_scanner = NmapScanner()
        logger.info("网络收集器初始化完成")
    
    def collect(self, target: str) -> Dict[str, Any]:
        """
        收集网络层信息
        
        Args:
            target: 目标IP或IP段
            
        Returns:
            Dict: 网络信息
        """
        logger.info(f"开始收集网络信息：{target}")
        
        network_info = {
            'target': target,
            'scan_time': datetime.now().isoformat(),
            'hosts': [],
            'network_topology': {},
            'open_ports_summary': {},
            'services_summary': {},
            'os_summary': {}
        }
        
        try:
            # 使用现有的Nmap扫描器
            if '/' in target:  # IP段
                scan_results = self.nmap_scanner.scan_ip_segment(target)
            else:  # 单个IP
                scan_results = self.nmap_scanner._scan_single_ip(target)
            
            # 处理扫描结果
            if scan_results:
                network_info.update(self._process_nmap_results(scan_results))
            
            # 额外的网络发现
            network_info['network_topology'] = self._discover_network_topology(target)
            
        except Exception as e:
            logger.error(f"网络信息收集失败：{e}")
            network_info['error'] = str(e)
        
        return network_info
    
    def _process_nmap_results(self, scan_results: Dict) -> Dict[str, Any]:
        """处理Nmap扫描结果"""
        processed = {
            'hosts': [],
            'open_ports_summary': {},
            'services_summary': {},
            'os_summary': {}
        }
        
        # 处理主机信息
        for host_ip, host_data in scan_results.get('scan', {}).items():
            host_info = {
                'ip': host_ip,
                'status': host_data.get('status', {}).get('state', 'unknown'),
                'hostname': host_data.get('hostnames', []),
                'ports': [],
                'os': {},
                'mac': {}
            }
            
            # 处理端口信息
            for protocol in ['tcp', 'udp']:
                if protocol in host_data:
                    for port, port_data in host_data[protocol].items():
                        port_info = {
                            'port': port,
                            'protocol': protocol,
                            'state': port_data.get('state', 'unknown'),
                            'service': port_data.get('name', ''),
                            'version': port_data.get('version', ''),
                            'product': port_data.get('product', ''),
                            'extrainfo': port_data.get('extrainfo', ''),
                            'banner': port_data.get('banner', '')
                        }
                        host_info['ports'].append(port_info)
            
            # 处理操作系统信息
            if 'osmatch' in host_data:
                for os_match in host_data['osmatch']:
                    host_info['os'] = {
                        'name': os_match.get('name', ''),
                        'accuracy': os_match.get('accuracy', 0),
                        'line': os_match.get('line', 0)
                    }
                    break  # 只取第一个匹配
            
            # 处理MAC地址信息
            if 'addresses' in host_data:
                for addr_type, addr_info in host_data['addresses'].items():
                    if addr_type == 'mac':
                        host_info['mac'] = {
                            'address': addr_info.get('addr', ''),
                            'vendor': addr_info.get('vendor', '')
                        }
            
            processed['hosts'].append(host_info)
        
        return processed
    
    def _discover_network_topology(self, target: str) -> Dict[str, Any]:
        """发现网络拓扑"""
        topology = {
            'gateway': None,
            'network_range': None,
            'subnet_mask': None,
            'dns_servers': [],
            'route_info': []
        }
        
        try:
            # 这里可以添加网络拓扑发现逻辑
            # 例如：traceroute、ARP扫描等
            pass
        except Exception as e:
            logger.error(f"网络拓扑发现失败：{e}")
        
        return topology


class WebCollector:
    """Web应用层信息收集器"""
    
    def collect(self, target: str) -> Dict[str, Any]:
        """
        收集Web应用信息
        
        Args:
            target: 目标域名或IP
            
        Returns:
            Dict: Web应用信息
        """
        logger.info(f"开始收集Web信息：{target}")
        
        web_info = {
            'target': target,
            'scan_time': datetime.now().isoformat(),
            'web_services': [],
            'technologies': [],
            'vulnerabilities': [],
            'api_endpoints': [],
            'directories': [],
            'forms': [],
            'cookies': [],
            'headers': {}
        }
        
        try:
            # API扫描
            api_results = scan_api(target)
            if api_results:
                web_info.update(api_results)
            
            # 网页内容爬取
            spider_results = crawl_website(target)
            if spider_results:
                web_info.update(spider_results)
            
            # AWVS漏洞扫描
            awvs_results = perform_awvs_scan(target)
            if awvs_results and awvs_results.get('status') == 'completed':
                web_info['vulnerabilities'] = awvs_results.get('results', {})
            
            # 技术栈识别
            web_info['technologies'] = self._identify_technologies(target)
            
            # API接口发现
            web_info['api_endpoints'] = self._discover_api_endpoints(target)
            
        except Exception as e:
            logger.error(f"Web信息收集失败：{e}")
            web_info['error'] = str(e)
        
        return web_info
    
    def _identify_technologies(self, target: str) -> List[Dict[str, Any]]:
        """识别Web技术栈"""
        technologies = []
        
        try:
            # 这里可以集成Wappalyzer或类似工具
            # 目前返回空列表，后续可以扩展
            pass
        except Exception as e:
            logger.error(f"技术栈识别失败：{e}")
        
        return technologies
    
    def _discover_api_endpoints(self, target: str) -> List[Dict[str, Any]]:
        """发现API接口"""
        endpoints = []
        
        try:
            # 这里可以添加API发现逻辑
            # 例如：目录爆破、JS文件分析等
            pass
        except Exception as e:
            logger.error(f"API接口发现失败：{e}")
        
        return endpoints


class SystemCollector:
    """系统层信息收集器"""
    
    def collect(self, target: str) -> Dict[str, Any]:
        """
        收集系统层信息
        
        Args:
            target: 目标IP或域名
            
        Returns:
            Dict: 系统信息
        """
        logger.info(f"开始收集系统信息：{target}")
        
        system_info = {
            'target': target,
            'scan_time': datetime.now().isoformat(),
            'file_system': {},
            'user_accounts': [],
            'network_config': {},
            'security_config': {},
            'installed_software': [],
            'running_processes': [],
            'system_logs': []
        }
        
        try:
            # 文件系统扫描
            system_info['file_system'] = self._scan_file_system(target)
            
            # 用户账户枚举
            system_info['user_accounts'] = self._enumerate_users(target)
            
            # 网络配置检查
            system_info['network_config'] = self._check_network_config(target)
            
            # 安全配置评估
            system_info['security_config'] = self._assess_security_config(target)
            
        except Exception as e:
            logger.error(f"系统信息收集失败：{e}")
            system_info['error'] = str(e)
        
        return system_info
    
    def _scan_file_system(self, target: str) -> Dict[str, Any]:
        """扫描文件系统"""
        return {
            'sensitive_files': [],
            'backup_files': [],
            'config_files': [],
            'log_files': [],
            'temp_files': []
        }
    
    def _enumerate_users(self, target: str) -> List[Dict[str, Any]]:
        """枚举用户账户"""
        return []
    
    def _check_network_config(self, target: str) -> Dict[str, Any]:
        """检查网络配置"""
        return {
            'interfaces': [],
            'routing_table': [],
            'firewall_rules': [],
            'listening_ports': []
        }
    
    def _assess_security_config(self, target: str) -> Dict[str, Any]:
        """评估安全配置"""
        return {
            'password_policy': {},
            'access_controls': [],
            'encryption_settings': {},
            'audit_settings': {}
        }


class ThreatIntelCollector:
    """威胁情报收集器"""
    
    def collect(self, target: str) -> Dict[str, Any]:
        """
        收集威胁情报
        
        Args:
            target: 目标IP或域名
            
        Returns:
            Dict: 威胁情报信息
        """
        logger.info(f"开始收集威胁情报：{target}")
        
        threat_intel = {
            'target': target,
            'scan_time': datetime.now().isoformat(),
            'reputation': {},
            'malware_history': [],
            'attack_history': [],
            'threat_indicators': [],
            'geolocation': {},
            'whois_info': {}
        }
        
        try:
            # IP/域名信誉检查
            threat_intel['reputation'] = self._check_reputation(target)
            
            # 地理位置信息
            threat_intel['geolocation'] = self._get_geolocation(target)
            
            # Whois信息
            threat_intel['whois_info'] = self._get_whois_info(target)
            
            # 威胁指标检查
            threat_intel['threat_indicators'] = self._check_threat_indicators(target)
            
        except Exception as e:
            logger.error(f"威胁情报收集失败：{e}")
            threat_intel['error'] = str(e)
        
        return threat_intel
    
    def _check_reputation(self, target: str) -> Dict[str, Any]:
        """检查IP/域名信誉"""
        return {
            'malicious': False,
            'suspicious': False,
            'reputation_score': 0,
            'sources': []
        }
    
    def _get_geolocation(self, target: str) -> Dict[str, Any]:
        """获取地理位置信息"""
        return {
            'country': '',
            'region': '',
            'city': '',
            'latitude': 0,
            'longitude': 0,
            'isp': '',
            'organization': ''
        }
    
    def _get_whois_info(self, target: str) -> Dict[str, Any]:
        """获取Whois信息"""
        return {
            'registrar': '',
            'creation_date': '',
            'expiration_date': '',
            'registrant': {},
            'admin_contact': {},
            'tech_contact': {}
        }
    
    def _check_threat_indicators(self, target: str) -> List[Dict[str, Any]]:
        """检查威胁指标"""
        return []


class AISecurityAnalyzer:
    """AI安全分析器"""
    
    def analyze_comprehensive(self, collected_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        对收集的信息进行全面的AI分析
        
        Args:
            collected_info: 收集到的所有信息
            
        Returns:
            Dict: AI分析结果
        """
        logger.info("开始AI安全分析")
        
        analysis_result = {
            'analysis_time': datetime.now().isoformat(),
            'vulnerability_assessment': {},
            'risk_score': 0,
            'attack_vectors': [],
            'recommendations': [],
            'threat_level': 'unknown'
        }
        
        try:
            # 使用现有的LLM分析功能
            llm_analysis = analyze_with_llm(json.dumps(collected_info, ensure_ascii=False))
            
            if llm_analysis.get('status') == 'success':
                analysis_result.update(llm_analysis.get('analysis', {}))
            
        except Exception as e:
            logger.error(f"AI分析失败：{e}")
            analysis_result['error'] = str(e)
        
        return analysis_result


# 使用示例
if __name__ == "__main__":
    # 创建全面信息收集器
    collector = ComprehensiveCollector(max_workers=5)
    
    # 收集目标信息
    target = "192.168.1.1"  # 示例目标
    results = collector.collect_target_info(target)
    
    # 输出结果
    print(json.dumps(results, indent=2, ensure_ascii=False))
