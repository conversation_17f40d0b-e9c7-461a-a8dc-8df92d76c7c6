"""
存储集成测试脚本

功能：
1. 测试存储管理器的各项功能
2. 验证数据存储的完整性
3. 测试不同类型扫描结果的存储
4. 验证数据库表结构

作者：AI渗透测试系统
版本：1.0.0
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from info_collector.database_storage.storage_manager import DatabaseStorageManager
from info_collector.database_storage.storage_integration import StorageIntegrator, get_storage_integrator


class StorageIntegrationTester:
    """存储集成测试器"""
    
    def __init__(self):
        self.storage_manager = DatabaseStorageManager()
        self.storage_integrator = StorageIntegrator()
        
        print("存储集成测试器初始化完成")
    
    def test_basic_storage_functions(self):
        """测试基础存储功能"""
        print("\n" + "=" * 60)
        print("测试基础存储功能")
        print("=" * 60)
        
        # 测试目标资产存储
        print("\n1. 测试目标资产存储")
        asset_data = {
            'asset_type': 'ip',
            'asset_value': '*************',
            'asset_name': 'Test Server',
            'description': '测试服务器',
            'business_importance': 'medium',
            'status': 'active',
            'confidence_score': 1.0,
            'metadata': {
                'test': True,
                'created_by': 'test_script'
            }
        }
        
        asset_id = self.storage_manager.store_target_asset(asset_data)
        if asset_id:
            print(f"✅ 成功存储目标资产，ID: {asset_id}")
        else:
            print("❌ 存储目标资产失败")
            return False
        
        # 测试网络信息存储
        print("\n2. 测试网络信息存储")
        network_data = {
            'ip_address': '*************',
            'hostname': 'test-server.local',
            'os_type': 'Linux',
            'os_version': 'Ubuntu 20.04',
            'os_accuracy': 95,
            'response_time': 10,
            'ttl': 64,
            'scan_method': 'nmap'
        }
        
        success = self.storage_manager.store_network_info(asset_id, network_data)
        if success:
            print("✅ 成功存储网络信息")
        else:
            print("❌ 存储网络信息失败")
        
        # 测试服务信息存储
        print("\n3. 测试服务信息存储")
        service_data = {
            'ip_address': '*************',
            'port': 80,
            'protocol': 'tcp',
            'service_name': 'http',
            'service_version': 'Apache 2.4.41',
            'service_product': 'Apache httpd',
            'service_banner': 'Apache/2.4.41 (Ubuntu)',
            'state': 'open',
            'confidence_score': 1.0
        }
        
        success = self.storage_manager.store_service_info(asset_id, service_data)
        if success:
            print("✅ 成功存储服务信息")
        else:
            print("❌ 存储服务信息失败")
        
        # 测试Web应用信息存储
        print("\n4. 测试Web应用信息存储")
        web_data = {
            'url': 'http://*************',
            'domain': 'test-server.local',
            'ip_address': '*************',
            'port': 80,
            'protocol': 'http',
            'title': 'Test Server Homepage',
            'server': 'Apache/2.4.41',
            'cms_type': 'WordPress',
            'cms_version': '5.8',
            'response_code': 200,
            'content_type': 'text/html'
        }
        
        success = self.storage_manager.store_web_app_info(asset_id, web_data)
        if success:
            print("✅ 成功存储Web应用信息")
        else:
            print("❌ 存储Web应用信息失败")
        
        # 测试漏洞信息存储
        print("\n5. 测试漏洞信息存储")
        vuln_data = {
            'vuln_type': 'web',
            'vuln_name': 'SQL Injection',
            'severity': 'high',
            'cvss_score': 8.5,
            'cve_id': 'CVE-2023-12345',
            'description': 'SQL injection vulnerability in login form',
            'solution': 'Use parameterized queries',
            'verified': True
        }
        
        success = self.storage_manager.store_vulnerability_info(asset_id, vuln_data)
        if success:
            print("✅ 成功存储漏洞信息")
        else:
            print("❌ 存储漏洞信息失败")
        
        return asset_id
    
    def test_comprehensive_scan_storage(self):
        """测试综合扫描结果存储"""
        print("\n" + "=" * 60)
        print("测试综合扫描结果存储")
        print("=" * 60)
        
        # 模拟comprehensive_collector的扫描结果
        comprehensive_results = {
            'target': '*************',
            'scan_time': datetime.now().isoformat(),
            'scan_duration': 120.5,
            'version': '1.0.0',
            'network_scan': {
                'hostname': 'web-server',
                'os_name': 'Linux',
                'os_version': 'CentOS 7',
                'os_accuracy': 90,
                'response_time': 15,
                'ttl': 64
            },
            'port_scan': {
                'open_ports': [
                    {
                        'port': 22,
                        'protocol': 'tcp',
                        'service': 'ssh',
                        'version': 'OpenSSH 7.4',
                        'product': 'OpenSSH',
                        'confidence': 10
                    },
                    {
                        'port': 80,
                        'protocol': 'tcp',
                        'service': 'http',
                        'version': 'nginx 1.16.1',
                        'product': 'nginx',
                        'confidence': 10
                    },
                    {
                        'port': 443,
                        'protocol': 'tcp',
                        'service': 'https',
                        'version': 'nginx 1.16.1',
                        'product': 'nginx',
                        'confidence': 10
                    }
                ]
            },
            'web_scan': {
                'http://*************': {
                    'title': 'Welcome to nginx!',
                    'server': 'nginx/1.16.1',
                    'status_code': 200,
                    'content_type': 'text/html',
                    'technologies': ['nginx', 'HTML']
                },
                'https://*************': {
                    'title': 'Secure Site',
                    'server': 'nginx/1.16.1',
                    'status_code': 200,
                    'content_type': 'text/html',
                    'technologies': ['nginx', 'SSL', 'HTML']
                }
            },
            'vulnerabilities': [
                {
                    'type': 'network',
                    'name': 'SSH Weak Encryption',
                    'severity': 'medium',
                    'cvss_score': 5.3,
                    'description': 'SSH server supports weak encryption algorithms',
                    'solution': 'Update SSH configuration to disable weak ciphers'
                },
                {
                    'type': 'web',
                    'name': 'Missing Security Headers',
                    'severity': 'low',
                    'cvss_score': 3.1,
                    'description': 'Web server missing security headers',
                    'solution': 'Configure security headers in web server'
                }
            ]
        }
        
        # 使用存储集成器存储结果
        success = self.storage_integrator.store_comprehensive_collector_results(comprehensive_results)
        if success:
            print("✅ 成功存储综合扫描结果")
        else:
            print("❌ 存储综合扫描结果失败")
        
        return success
    
    def test_awvs_scan_storage(self):
        """测试AWVS扫描结果存储"""
        print("\n" + "=" * 60)
        print("测试AWVS扫描结果存储")
        print("=" * 60)
        
        # 模拟AWVS扫描结果
        awvs_results = {
            'status': 'completed',
            'target_id': 'target-123',
            'scan_id': 'scan-456',
            'results': {
                'vulnerabilities': [
                    {
                        'vuln_name': 'Cross-Site Scripting (XSS)',
                        'severity': 'high',
                        'cvss_score': 7.2,
                        'description': 'Reflected XSS vulnerability in search parameter',
                        'recommendation': 'Implement proper input validation and output encoding',
                        'affects': '/search.php?q=<script>alert(1)</script>',
                        'request': 'GET /search.php?q=<script>alert(1)</script> HTTP/1.1',
                        'response': 'HTTP/1.1 200 OK\nContent-Type: text/html\n\n<script>alert(1)</script>'
                    },
                    {
                        'vuln_name': 'SQL Injection',
                        'severity': 'critical',
                        'cvss_score': 9.8,
                        'description': 'SQL injection in login form',
                        'recommendation': 'Use parameterized queries',
                        'affects': '/login.php',
                        'request': 'POST /login.php HTTP/1.1\nContent-Type: application/x-www-form-urlencoded\n\nusername=admin\' OR 1=1--&password=test',
                        'response': 'HTTP/1.1 200 OK\nContent-Type: text/html\n\nWelcome admin!'
                    }
                ]
            }
        }
        
        domain = 'test-website.com'
        
        # 使用存储集成器存储结果
        success = self.storage_integrator.store_awvs_results(domain, awvs_results)
        if success:
            print("✅ 成功存储AWVS扫描结果")
        else:
            print("❌ 存储AWVS扫描结果失败")
        
        return success
    
    def test_database_scan_storage(self):
        """测试数据库扫描结果存储"""
        print("\n" + "=" * 60)
        print("测试数据库扫描结果存储")
        print("=" * 60)
        
        # 模拟数据库扫描结果
        db_results = {
            'target': '*************',
            'scan_time': datetime.now().isoformat(),
            'scan_duration': 45.2,
            'discovered_databases': [
                {
                    'type': 'MySQL',
                    'host': '*************',
                    'port': 3306,
                    'version': '8.0.25',
                    'authentication': {
                        'weak_credentials': [
                            {'username': 'root', 'password': ''}
                        ]
                    },
                    'databases': ['information_schema', 'mysql', 'test'],
                    'users': ['root@localhost', 'mysql@localhost'],
                    'configuration': {
                        'ssl_enabled': False,
                        'general_log': 'OFF'
                    },
                    'ssl_enabled': False
                }
            ],
            'vulnerabilities': [
                {
                    'type': 'configuration',
                    'severity': 'critical',
                    'description': 'MySQL root account has empty password',
                    'recommendation': 'Set a strong password for the root account',
                    'cve': None
                },
                {
                    'type': 'configuration',
                    'severity': 'medium',
                    'description': 'SSL/TLS encryption is not enabled',
                    'recommendation': 'Enable SSL/TLS encryption for secure connections',
                    'cve': None
                }
            ]
        }
        
        # 使用存储集成器存储结果
        success = self.storage_integrator.store_database_scan_results('*************', db_results)
        if success:
            print("✅ 成功存储数据库扫描结果")
        else:
            print("❌ 存储数据库扫描结果失败")
        
        return success
    
    def test_storage_statistics(self):
        """测试存储统计功能"""
        print("\n" + "=" * 60)
        print("测试存储统计功能")
        print("=" * 60)
        
        try:
            stats = self.storage_integrator.get_storage_statistics()
            
            print("存储统计信息：")
            print(json.dumps(stats, indent=2, ensure_ascii=False))
            
            if stats:
                print("✅ 成功获取存储统计信息")
                return True
            else:
                print("❌ 获取存储统计信息失败")
                return False
                
        except Exception as e:
            print(f"❌ 获取存储统计信息异常：{e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始存储集成测试")
        print("测试时间：", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        test_results = {
            'basic_storage': False,
            'comprehensive_scan': False,
            'awvs_scan': False,
            'database_scan': False,
            'statistics': False
        }
        
        try:
            # 1. 测试基础存储功能
            asset_id = self.test_basic_storage_functions()
            test_results['basic_storage'] = bool(asset_id)
            
            # 2. 测试综合扫描结果存储
            test_results['comprehensive_scan'] = self.test_comprehensive_scan_storage()
            
            # 3. 测试AWVS扫描结果存储
            test_results['awvs_scan'] = self.test_awvs_scan_storage()
            
            # 4. 测试数据库扫描结果存储
            test_results['database_scan'] = self.test_database_scan_storage()
            
            # 5. 测试存储统计功能
            test_results['statistics'] = self.test_storage_statistics()
            
        except Exception as e:
            print(f"测试过程中发生异常：{e}")
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)
        
        total_tests = len(test_results)
        passed_tests = sum(test_results.values())
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        print(f"\n总计：{passed_tests}/{total_tests} 个测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！存储集成系统工作正常")
        else:
            print("⚠️  部分测试失败，请检查配置和数据库连接")
        
        return test_results


def main():
    """主函数"""
    tester = StorageIntegrationTester()
    results = tester.run_all_tests()
    
    # 返回测试结果
    return results


if __name__ == "__main__":
    main()
