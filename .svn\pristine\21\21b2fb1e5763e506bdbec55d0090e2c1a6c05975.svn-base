'''
    这个文件主要用于扫描获取web网站中的api信息
    首先从表中获取要扫描的域名
    然后对域名进行探测，获取所有的api信息
    最后将api信息存入数据库
'''

import pymysql
import requests
import configparser
import traceback
import time
from typing import Optional, Dict, Any

def get_db_connection():
    """从配置文件中获取数据库连接"""
    config = configparser.ConfigParser()
    config.read('..\\..\\config\\config.config', encoding='utf-8')
    db_config = {
        'host': config['mysql']['host'],
        'port': int(config['mysql']['port']),
        'user': config['mysql']['user'],
        'password': config['mysql']['password'],
        'database': config['mysql']['database']
    }
    return pymysql.connect(**db_config)

def get_todo_domain() -> Optional[str]:
    """
    从domain_ip_mapping表中获取待扫描的域名
    
    Returns:
        Optional[str]: 待扫描的域名，如果没有则返回None
    """
    try:
        db = get_db_connection()
        cursor = db.cursor()
        # 从domain_ip_mapping表中查询一条api_status = 'todo'的域名
        query = """
                SELECT domain 
                FROM domain_ip_mapping 
                WHERE api_status = 'todo'
                LIMIT 1
                """
        cursor.execute(query)
        domain = cursor.fetchone()
        cursor.close()
        db.close()
        if domain:
            print(f'获取到待扫描域名: {domain[0]}')
            return domain[0]
        return None
    except Exception as e:
        print(f"获取待扫描域名时出错: {e}")
        traceback.print_exc()
        return None

def scan_apis(domain: str) -> Dict[str, Any]:
    """
    对域名进行API探测
    
    Args:
        domain (str): 要扫描的域名
        
    Returns:
        Dict[str, Any]: API扫描结果
    """
    print(f"开始对 {domain} 进行API扫描")
    
    try:
        # 这里可以添加实际的API扫描逻辑
        # 示例：使用requests探测常见API端点
        api_endpoints = [
            f"https://{domain}/api/v1",
            f"https://{domain}/api/v2",
            f"https://{domain}/graphql",
            f"https://{domain}/rest",
            f"https://{domain}/soap",
            f"https://{domain}/admin",
            f"https://{domain}/login",
            f"https://{domain}/swagger",
            f"https://{domain}/api-docs",
            f"https://{domain}/v1/api-docs",
            f"https://{domain}/v2/api-docs",
            f"https://{domain}/console",
            f"https://{domain}/manager",
            f"https://{domain}/actuator",
            f"https://{domain}/health",
            f"https://{domain}/env",
            f"https://{domain}/metrics",
            f"https://{domain}/phpmyadmin",
            f"https://{domain}/dbadmin",
            f"https://{domain}/wp-admin",
            f"https://{domain}/_ignition/execute-solution"
        ]
        
        results = {}
        for endpoint in api_endpoints:
            try:
                response = requests.get(endpoint, timeout=5, verify=False)
                if response.status_code < 400:
                    results[endpoint] = {
                        'status_code': response.status_code,
                        'headers': dict(response.headers),
                        'content_type': response.headers.get('Content-Type')
                    }
            except requests.RequestException:
                continue
        
        return {
            'status': 'completed',
            'domain': domain,
            'results': results
        }
    except Exception as e:
        print(f"API扫描时出错: {e}")
        traceback.print_exc()
        return {'status': 'error', 'details': str(e)}

def save_api_results(domain: str, results: Dict[str, Any]) -> bool:
    """
    将API扫描结果存入数据库
    
    Args:
        domain (str): 扫描的域名
        results (Dict[str, Any]): 扫描结果
        
    Returns:
        bool: 是否保存成功
    """
    try:
        db = get_db_connection()
        cursor = db.cursor()
        
        # 检查并创建web_api_results表
        create_table_query = """
        CREATE TABLE IF NOT EXISTS web_api_results (
            id INT AUTO_INCREMENT PRIMARY KEY,
            domain VARCHAR(255) NOT NULL,
            api_results TEXT,
            scan_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )"""
        cursor.execute(create_table_query)
        
        # 插入扫描结果到新表
        insert_query = """
        INSERT INTO web_api_results (domain, api_results)
        VALUES (%s, %s)
        """
        cursor.execute(insert_query, (domain, str(results)))
        
        # 更新domain_ip_mapping表中的扫描状态
        update_query = """
        UPDATE domain_ip_mapping 
        SET api_status = 'done'
        WHERE domain = %s
        """
        cursor.execute(update_query, (domain,))
        db.commit()
        cursor.close()
        db.close()
        print(f"成功保存 {domain} 的API扫描结果")
        return True
    except Exception as e:
        print(f"保存API扫描结果时出错: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数，执行API扫描流程"""
    while True:
        domain = get_todo_domain()
        if not domain:
            print("没有待扫描的域名，等待60秒...")
            time.sleep(60)
            continue
        
        results = scan_apis(domain)
        if results.get('status') == 'completed':
            save_api_results(domain, results)
        else:
            print(f"扫描 {domain} 失败: {results.get('details')}")
        
        # 短暂暂停避免过高频率扫描
        time.sleep(5)


main()