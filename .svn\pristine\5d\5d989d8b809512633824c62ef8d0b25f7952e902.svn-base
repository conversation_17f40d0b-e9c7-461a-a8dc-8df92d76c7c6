'''
信息集中器主程序

功能：
1. 协调各个模块的工作
2. 实现完整的信息收集、分析、攻击路径生成和执行流程
3. 提供命令行接口

使用方法：
python main.py [--target TARGET] [--mode MODE]

参数：
  --target TARGET  指定目标IP或IP段，如果不指定则从数据库获取
  --mode MODE      运行模式：full(完整流程)、collect(仅收集信息)、analyze(仅分析)、attack(仅攻击)
'''

import os
import sys
import json
import time
import argparse
import logging
from typing import Dict, List, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(__file__), 'info_concentrator.log'))
    ]
)
logger = logging.getLogger('info_concentrator')

# 导入各个模块
from info_concentrator import info_concentrator
from info_concentrator import attack_path_generator
from info_concentrator import attack_executor


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='信息集中器 - 自动化渗透测试工具')
    parser.add_argument('--target', help='目标IP或IP段，如果不指定则从数据库获取')
    parser.add_argument('--mode', choices=['full', 'collect', 'analyze', 'attack'], default='full',
                        help='运行模式：full(完整流程)、collect(仅收集信息)、analyze(仅分析)、attack(仅攻击)')
    return parser.parse_args()


def run_full_process(target: Optional[str] = None):
    """运行完整流程
    
    参数:
        target: 目标IP或IP段，如果不指定则从数据库获取
    """
    logger.info("启动完整渗透测试流程...")
    
    # 获取目标IP段
    ip_segments = []
    if target:
        ip_segments = [target]
    else:
        ip_segments = info_concentrator.get_target_ips()
        if not ip_segments:
            logger.info("没有找到待扫描的IP段，尝试获取新的IP段...")
            ip_segment = info_concentrator.get_ip_segments()
            if ip_segment:
                ip_segments = [ip_segment]
    
    if not ip_segments:
        logger.error("未找到目标IP段，退出程序")
        return
    
    logger.info(f"找到{len(ip_segments)}个待扫描的IP段: {', '.join(ip_segments)}")
    
    # 处理每个IP段
    for ip_segment in ip_segments:
        logger.info(f"\n处理IP段: {ip_segment}")
        
        # 更新IP段状态为'scanning'
        info_concentrator.update_ip_segment_status(ip_segment, 'scanning')
        
        try:
            # 收集网络信息
            network_info = info_concentrator.collect_network_info(ip_segment)
            
            # 对每个活跃主机收集Web信息并进行分析
            for host in network_info.get('hosts', []):
                if host.get('status') == 'up':
                    ip = host.get('ip')
                    logger.info(f"\n处理活跃主机: {ip}")
                    
                    # 收集Web信息
                    web_info = info_concentrator.collect_web_info(ip)
                    
                    # 整合目标信息
                    target_info = {
                        "network": host,
                        "web": web_info
                    }
                    
                    # 分析安全信息
                    analysis_result = info_concentrator.analyze_security_info(host, web_info)
                    
                    if analysis_result.get('status') == 'success':
                        # 生成攻击路径
                        attack_path = attack_path_generator.generate_attack_path(analysis_result, target_info)
                        
                        if attack_path.get('status') == 'success':
                            # 执行攻击
                            execution_result = attack_executor.execute_attack(attack_path)
                            
                            # 保存攻击结果
                            info_concentrator.save_attack_result(ip, attack_path, execution_result)
                            
                            # 如果攻击失败，尝试其他攻击路径
                            if execution_result.get('status') != 'success':
                                logger.info(f"攻击失败，尝试生成其他攻击路径...")
                                
                                # 分析失败原因
                                failure_analysis = attack_path_generator.analyze_attack_failure(
                                    execution_result, attack_path)
                                
                                # 生成替代攻击路径
                                alternative_attack_path = attack_path_generator.generate_alternative_attack_path(
                                    analysis_result, target_info, {
                                        "attack_path": attack_path,
                                        "execution_result": execution_result,
                                        "failure_analysis": failure_analysis.get('failure_analysis', {})
                                    })
                                
                                if alternative_attack_path.get('status') == 'success':
                                    # 执行替代攻击
                                    alternative_execution_result = attack_executor.execute_attack(alternative_attack_path)
                                    
                                    # 保存替代攻击结果
                                    info_concentrator.save_attack_result(
                                        ip, alternative_attack_path, alternative_execution_result)
                        else:
                            logger.error(f"生成攻击路径失败: {attack_path.get('details')}")
                    else:
                        logger.error(f"分析安全信息失败: {analysis_result.get('details')}")
            
            # 更新IP段状态为'done'
            info_concentrator.update_ip_segment_status(ip_segment, 'done')
            
        except Exception as e:
            logger.error(f"处理IP段{ip_segment}时出错: {e}")
            # 更新IP段状态为'error'
            info_concentrator.update_ip_segment_status(ip_segment, 'error')
    
    logger.info("\n信息集中器任务完成")


def run_collect_only(target: Optional[str] = None):
    """仅运行信息收集流程
    
    参数:
        target: 目标IP或IP段，如果不指定则从数据库获取
    """
    logger.info("启动信息收集流程...")
    
    # 获取目标IP段
    ip_segments = []
    if target:
        ip_segments = [target]
    else:
        ip_segments = info_concentrator.get_target_ips()
        if not ip_segments:
            logger.info("没有找到待扫描的IP段，尝试获取新的IP段...")
            ip_segment = info_concentrator.get_ip_segments()
            if ip_segment:
                ip_segments = [ip_segment]
    
    if not ip_segments:
        logger.error("未找到目标IP段，退出程序")
        return
    
    logger.info(f"找到{len(ip_segments)}个待扫描的IP段: {', '.join(ip_segments)}")
    
    # 处理每个IP段
    for ip_segment in ip_segments:
        logger.info(f"\n处理IP段: {ip_segment}")
        
        # 更新IP段状态为'scanning'
        info_concentrator.update_ip_segment_status(ip_segment, 'scanning')
        
        try:
            # 收集网络信息
            network_info = info_concentrator.collect_network_info(ip_segment)
            
            # 对每个活跃主机收集Web信息
            for host in network_info.get('hosts', []):
                if host.get('status') == 'up':
                    ip = host.get('ip')
                    logger.info(f"\n处理活跃主机: {ip}")
                    
                    # 收集Web信息
                    web_info = info_concentrator.collect_web_info(ip)
                    
                    # 保存收集到的信息
                    save_collected_info(ip, host, web_info)
            
            # 更新IP段状态为'collected'
            info_concentrator.update_ip_segment_status(ip_segment, 'collected')
            
        except Exception as e:
            logger.error(f"处理IP段{ip_segment}时出错: {e}")
            # 更新IP段状态为'error'
            info_concentrator.update_ip_segment_status(ip_segment, 'error')
    
    logger.info("\n信息收集任务完成")


def save_collected_info(ip: str, network_info: Dict[str, Any], web_info: Dict[str, Any]):
    """保存收集到的信息
    
    参数:
        ip: 目标IP
        network_info: 网络信息
        web_info: Web信息
    """
    try:
        # 获取数据库连接
        db = info_concentrator.get_db_connection()
        cursor = db.cursor()
        
        # 创建收集信息表(如果不存在)
        create_table = """
        CREATE TABLE IF NOT EXISTS collected_info (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip VARCHAR(255) NOT NULL,
            network_info JSON,
            web_info JSON,
            collect_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY ip_unique (ip)
        )"""
        cursor.execute(create_table)
        
        # 插入或更新结果
        query = """
        INSERT INTO collected_info 
        (ip, network_info, web_info)
        VALUES (%s, %s, %s)
        ON DUPLICATE KEY UPDATE 
            network_info = VALUES(network_info),
            web_info = VALUES(web_info),
            collect_time = CURRENT_TIMESTAMP
        """
        cursor.execute(
            query, 
            (
                ip, 
                json.dumps(network_info), 
                json.dumps(web_info)
            )
        )
        db.commit()
        
        cursor.close()
        db.close()
        logger.info(f"成功保存收集信息: {ip}")
    except Exception as e:
        logger.error(f"保存收集信息时出错: {e}")


def run_analyze_only(target: Optional[str] = None):
    """仅运行分析流程
    
    参数:
        target: 目标IP，如果不指定则分析所有已收集信息的IP
    """
    logger.info("启动安全分析流程...")
    
    try:
        # 获取数据库连接
        db = info_concentrator.get_db_connection()
        cursor = db.cursor()
        
        # 查询已收集信息的IP
        if target:
            query = "SELECT ip, network_info, web_info FROM collected_info WHERE ip = %s"
            cursor.execute(query, (target,))
        else:
            query = "SELECT ip, network_info, web_info FROM collected_info"
            cursor.execute(query)
        
        results = cursor.fetchall()
        cursor.close()
        db.close()
        
        if not results:
            logger.error("未找到已收集信息的IP，退出程序")
            return
        
        logger.info(f"找到{len(results)}个已收集信息的IP")
        
        # 处理每个IP
        for result in results:
            ip = result[0]
            network_info = json.loads(result[1])
            web_info = json.loads(result[2])
            
            logger.info(f"\n分析IP: {ip}")
            
            # 分析安全信息
            analysis_result = info_concentrator.analyze_security_info(network_info, web_info)
            
            if analysis_result.get('status') == 'success':
                # 保存分析结果
                info_concentrator.save_analysis_result(ip, analysis_result, 'combined')
                logger.info(f"成功分析IP: {ip}")
            else:
                logger.error(f"分析IP {ip} 失败: {analysis_result.get('details')}")
        
        logger.info("\n安全分析任务完成")
    except Exception as e:
        logger.error(f"运行分析流程时出错: {e}")


def run_attack_only(target: Optional[str] = None):
    """仅运行攻击流程
    
    参数:
        target: 目标IP，如果不指定则攻击所有已分析的IP
    """
    logger.info("启动攻击流程...")
    
    try:
        # 获取数据库连接
        db = info_concentrator.get_db_connection()
        cursor = db.cursor()
        
        # 查询已分析的IP
        if target:
            query = """
            SELECT s.target, s.analysis_result, c.network_info, c.web_info 
            FROM security_analysis_results s
            JOIN collected_info c ON s.target = c.ip
            WHERE s.target = %s
            """
            cursor.execute(query, (target,))
        else:
            query = """
            SELECT s.target, s.analysis_result, c.network_info, c.web_info 
            FROM security_analysis_results s
            JOIN collected_info c ON s.target = c.ip
            """
            cursor.execute(query)
        
        results = cursor.fetchall()
        cursor.close()
        db.close()
        
        if not results:
            logger.error("未找到已分析的IP，退出程序")
            return
        
        logger.info(f"找到{len(results)}个已分析的IP")
        
        # 处理每个IP
        for result in results:
            ip = result[0]
            analysis_result = json.loads(result[1])
            network_info = json.loads(result[2])
            web_info = json.loads(result[3])
            
            logger.info(f"\n攻击IP: {ip}")
            
            # 整合目标信息
            target_info = {
                "network": network_info,
                "web": web_info
            }
            
            # 生成攻击路径
            attack_path = attack_path_generator.generate_attack_path(
                {"status": "success", "analysis": analysis_result}, target_info)
            
            if attack_path.get('status') == 'success':
                # 执行攻击
                execution_result = attack_executor.execute_attack(attack_path)
                
                # 保存攻击结果
                info_concentrator.save_attack_result(ip, attack_path, execution_result)
                
                # 如果攻击失败，尝试其他攻击路径
                if execution_result.get('status') != 'success':
                    logger.info(f"攻击失败，尝试生成其他攻击路径...")
                    
                    # 分析失败原因
                    failure_analysis = attack_path_generator.analyze_attack_failure(
                        execution_result, attack_path)
                    
                    # 生成替代攻击路径
                    alternative_attack_path = attack_path_generator.generate_alternative_attack_path(
                        {"status": "success", "analysis": analysis_result}, target_info, {
                            "attack_path": attack_path,
                            "execution_result": execution_result,
                            "failure_analysis": failure_analysis.get('failure_analysis', {})
                        })
                    
                    if alternative_attack_path.get('status') == 'success':
                        # 执行替代攻击
                        alternative_execution_result = attack_executor.execute_attack(alternative_attack_path)
                        
                        # 保存替代攻击结果
                        info_concentrator.save_attack_result(
                            ip, alternative_attack_path, alternative_execution_result)
            else:
                logger.error(f"生成攻击路径失败: {attack_path.get('details')}")
        
        logger.info("\n攻击任务完成")
    except Exception as e:
        logger.error(f"运行攻击流程时出错: {e}")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 根据运行模式执行相应流程
    if args.mode == 'full':
        run_full_process(args.target)
    elif args.mode == 'collect':
        run_collect_only(args.target)
    elif args.mode == 'analyze':
        run_analyze_only(args.target)
    elif args.mode == 'attack':
        run_attack_only(args.target)
    else:
        logger.error(f"不支持的运行模式: {args.mode}")


if __name__ == "__main__":
    main()