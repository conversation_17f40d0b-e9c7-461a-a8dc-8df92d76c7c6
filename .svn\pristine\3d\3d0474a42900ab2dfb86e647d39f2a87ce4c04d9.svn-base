本目录提供一些MCP协议的使用例子<br>
1. mcpclient.py<br>
   mcp的客户端，包括与服务端，LLM的连接，使用claude模型<br>
   启动方法<br>
   uv run mcpclient.py /path/mcpsampleserver.py<br>
2. mcpsampleserver.py<br>
   简单的mcp服务端的例子，只包括 函数调用<br>
   启动方法<br>
   uv run mcpsampleserver.py <br>
3. clientdeepseek.py<br>
   MCP直接与deepseek对接会出错，有些格式的问题，稍作调整即可使用。<br>
   本例实现了deepseek与MCP协议的对接。<br>

详细的说明见<br>
   https://mp.weixin.qq.com/s/CpSsX5UV2MhfeEzGgGiGJg  <br>



更多信息请关注公众号 AI与安全

<img src="https://github.com/user-attachments/assets/79a42f67-97b8-4820-bce8-a2478fec5a25" width="150" height="150" alt="93da7391639673746ac5062a51e3e75">


